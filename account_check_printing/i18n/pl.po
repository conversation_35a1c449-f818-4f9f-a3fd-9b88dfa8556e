# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
msgid "%(journal)s: Check Number Sequence"
msgstr "%(journal)s: Sekwencja numerów czeków"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "<span>&amp;nbsp;</span>"
msgstr "<span>&amp;nbsp;</span>"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr ""
"Regulacja marginesów generowanych do kontroli, aby dopasować twoje "
"ustawienia drukarki."

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "Kwota słownie"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Bills"
msgstr "Faktury zakupu"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "Anuluj"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__bank_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "sprawdzić układ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "Sprawdź lewy margines"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "Numer czeku"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "Drukowanie czeku"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "Sprawdź prawy margines"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "Kolejność czeku"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "Sprawdź górny margines"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Check numbers can only consist of digits"
msgstr "Numery czeków mogą składać się tylko z cyfr"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""
"Zaznacz tę opcję, jeśli twoje wstępnie wydrukowane czeki nie są "
"ponumerowane."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "Zaznacz do druku"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "Czeki"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "Kolejność numeracji czeków."

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
msgid "Checks to Print"
msgstr "Czeki do druku"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "Czeki do druku"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Default"
msgstr "Domyślny"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Go to the configuration panel"
msgstr "Przejdź do panelu konfiguracji"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "ID"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""
"W celu wydrukowania za jednym razem kilku czeków, muszą one należeć do tego "
"samego dziennika bankowego."

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "Dziennik"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "Ręczne numerowanie"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"Instrukcja: Płać lub otrzymuj płatności za pomocą dowolnej metody poza Odoo.\n"
"Dostawcy płatności: Każdy dostawca płatności ma swoją własną Metodę płatności. Zażądaj transakcji na/do karty dzięki tokenowi płatniczemu zapisanemu przez partnera podczas zakupu lub subskrypcji online.\n"
"Czek: Płać rachunki czekiem i drukuj go z Odoo.\n"
"Batch Deposit: Zbierz kilka czeków klientów jednocześnie generując i składając depozyt wsadowy do swojego banku. Konieczny jest moduł account_batch_payment.\n"
"SEPA Credit Transfer: Płać w strefie SEPA składając plik SEPA Credit Transfer do swojego banku. Moduł account_sepa jest niezbędny.\n"
"SEPA Direct Debit: Otrzymuj płatności w strefie SEPA dzięki mandatowi, który udzieli Ci Twój partner. Moduł account_sepa jest niezbędny.\n"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "Wielostronicowy odcinek kontrolny"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "Numer następnego czeku"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
msgid "Next Check Number should only contains numbers."
msgstr "Następny numer kontrolny powinien zawierać tylko cyfry"

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "Brak"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
msgid "Payment Method"
msgstr "Metoda płatności"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr "Metody wysyłania płatności"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "Wpłaty"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""
"Płatności drukowane jako czeki muszą mieć zaznaczony \"Czek\" jako metoda "
"płatności i nie mogły zostać pogodzone"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr ""
"Wpisz numer pierwszego wstępnie wydrukowanego czeku, który chcesz "
"wydrukować."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "Drukuj"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "Drukuj czek"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "Drukuj czeki"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "Drukuj etykietę z datą"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print Pre-numbered Checks"
msgstr "Drukuj przenumerowane czeki"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid "Refunds"
msgstr "Korekty"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "Prawy margines"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"Wybierz format odpowiadający papierowi, na którym będą drukowane czeki.\n"
"Aby wyłączyć funkcję drukowania, wybierz \"None\"."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "Wysłane"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "Kolejny numer następnie wydrukowanego czeku"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__show_check_number
msgid "Show Check Number"
msgstr "Pokaż numer czeku"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr ""
"Coś poszło nie tak z Układem sprawdzania, proszę wybrać inny układ w "
"Ustawieniach fakturowania/księgowania i spróbować ponownie."

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr ""
"Następujące numery są już używane:\n"
"%s"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""
"Ostatnim numerem czeku był %s. W celu uniknięcia odrzucenia czeku przez bank"
" użyj wyższego numeru."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"Wybrany dziennik jest skonfigurowany, aby drukować numery czeków. Jeżeli "
"wstępnie wydrukowane czeki mają numery lub aktualna numeracja jest zła, "
"możesz zmienić ją na stronie konfiguracyjnej dziennika."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr ""
"Ta opcja pozwala na drukowanie sprawdzonych szczegółów (pośredniczących) na "
"wielu stronach, jeśli nie pasują na tej pojedynczej stronie."

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""
"Ta opcja umożliwia drukowanie etykiety z datą na czeku zgodnie z CPA.\n"
"Wyłącz to, jeśli twój wstępnie wydrukowany czek zawiera etykietę daty."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr "To pozwoli zapisać na płatnościach numer właściwego czeku."

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "Odznacz wysłane"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr "Pusty czek"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr ""
"Musisz wybrać układ czeków. W tym celu wejdź w Ustawienia "
"fakturowania/księgowości, wyszukaj \"Układ czeków\" i ustaw jeden."
