<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="cloud_storage_config_settings_view_form" model="ir.ui.view">
        <field name="name">cloud_storage_config_settings_view_form</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="70"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside" >
                <app data-string="Cloud Storage" string="Cloud Storage" name="cloud_storage">
                    <block title="Cloud Storage Settings">
                        <setting id="cloud_storage_provider" help="Select the cloud storage provider to store new attachments.">
                            <field name="cloud_storage_provider"/>
                        </setting>
                        <setting help="Minimum size(bytes) for attachments to be stored in the cloud storage"
                                 id="cloud_storage_min_file_size">
                            <field name="cloud_storage_min_file_size"/>
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>

</odoo>
