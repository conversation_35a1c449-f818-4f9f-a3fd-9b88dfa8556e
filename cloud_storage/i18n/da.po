# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cloud_storage
# 
# Translators:
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_attachment
msgid "Attachment"
msgstr "Vedhæftning"

#. module: cloud_storage
#: model:ir.model.fields.selection,name:cloud_storage.selection__ir_attachment__type__cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage"
msgstr ""

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_provider
msgid "Cloud Storage Provider for new attachments"
msgstr ""

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage Settings"
msgstr ""

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/ir_attachment.py:0
msgid "Cloud Storage is not enabled"
msgstr ""

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/controllers/attachment.py:0
msgid "Cloud storage configuration has been changed. Please refresh the page."
msgstr ""

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "Cloud storage error"
msgstr ""

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid "Minimum File Size (bytes)"
msgstr ""

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Minimum size(bytes) for attachments to be stored in the cloud storage"
msgstr ""

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/res_config_settings.py:0
msgid "Please configure the Cloud Storage before enabling it"
msgstr ""

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Select the cloud storage provider to store new attachments."
msgstr ""

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_ir_attachment__type
msgid "Type"
msgstr "Type"

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "You are not allowed to upload file to the cloud storage"
msgstr ""

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_ir_attachment__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Du kan enten overføre en fil fra din computer eller kopiere/indsætte en "
"internethenvisning i din fil."

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid ""
"webclient can upload files larger than the minimum file size\n"
"        (in bytes) as url attachments to the server and then upload the file to\n"
"        the cloud storage."
msgstr ""
