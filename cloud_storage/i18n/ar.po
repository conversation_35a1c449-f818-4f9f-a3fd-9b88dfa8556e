# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cloud_storage
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_attachment
msgid "Attachment"
msgstr "مرفق"

#. module: cloud_storage
#: model:ir.model.fields.selection,name:cloud_storage.selection__ir_attachment__type__cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage"
msgstr "مساحة التخزين السحابية "

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_provider
msgid "Cloud Storage Provider for new attachments"
msgstr "مزود مساحة التخزين السحابية للمرفقات الجديدة "

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage Settings"
msgstr "إعدادات مساحة التخزين السحابية "

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/ir_attachment.py:0
msgid "Cloud Storage is not enabled"
msgstr "لم يتم تمكين مساحة التخزين السحابية "

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/controllers/attachment.py:0
msgid "Cloud storage configuration has been changed. Please refresh the page."
msgstr "لقد تغيرت تهيئة مساحة التخزين السحابية. يُرجى تحديث الصفحة. "

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "Cloud storage error"
msgstr "خطأ في مساحة التخزين السحابية "

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid "Minimum File Size (bytes)"
msgstr "تقليل مساحة الملف (بايت) "

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Minimum size(bytes) for attachments to be stored in the cloud storage"
msgstr ""
"قم بتقليل حجم المرفقات (بايت) حتى يتم تخزينها في مساحة التخزين السحابية "

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/res_config_settings.py:0
msgid "Please configure the Cloud Storage before enabling it"
msgstr "يرجى تهيئة مساحة التخزين السحابية قبل تمكينها "

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Select the cloud storage provider to store new attachments."
msgstr "قم بتحديد مزود مساحة التخزين السحابية لتخزين مرفقات جديدة. "

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_ir_attachment__type
msgid "Type"
msgstr "النوع"

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "You are not allowed to upload file to the cloud storage"
msgstr "لا يُسمَح لك برفع الملف إلى مساحة التخزين السحابية "

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_ir_attachment__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr "يمكنك أن ترفع ملفًا من جهازك أو تنسخ وتلصق رابط الملف."

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid ""
"webclient can upload files larger than the minimum file size\n"
"        (in bytes) as url attachments to the server and then upload the file to\n"
"        the cloud storage."
msgstr ""
"بإمكان webclient رفع الملفات الأكبر من الحد الأدنى لحجم الملف\n"
"        (بايت) كمرفقات بتنسيق URL إلى الخادم ثم رفع الملف في\n"
"        مساحة التخزين السحابية. "
