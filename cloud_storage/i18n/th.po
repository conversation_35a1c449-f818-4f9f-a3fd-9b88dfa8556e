# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cloud_storage
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_attachment
msgid "Attachment"
msgstr "การแนบ"

#. module: cloud_storage
#: model:ir.model.fields.selection,name:cloud_storage.selection__ir_attachment__type__cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage"
msgstr "การเก็บข้อมูลบนคลาวด์"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_provider
msgid "Cloud Storage Provider for new attachments"
msgstr "ผู้ให้บริการระบบ Cloud Storage สำหรับไฟล์แนบใหม่"

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage Settings"
msgstr ""

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/ir_attachment.py:0
msgid "Cloud Storage is not enabled"
msgstr ""

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/controllers/attachment.py:0
msgid "Cloud storage configuration has been changed. Please refresh the page."
msgstr ""
"การกำหนดค่าการจัดเก็บข้อมูลบนคลาวด์ได้รับการเปลี่ยนแปลง โปรดรีเฟรชหน้า"

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "Cloud storage error"
msgstr ""

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid "Minimum File Size (bytes)"
msgstr ""

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Minimum size(bytes) for attachments to be stored in the cloud storage"
msgstr "ขนาดขั้นต่ำ (ไบต์) สำหรับสิ่งที่แนบมาที่จะจัดเก็บในระบบคลาวด์"

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/res_config_settings.py:0
msgid "Please configure the Cloud Storage before enabling it"
msgstr "กรุณาตั้งค่า Cloud Storage ก่อนเปิดใช้งาน"

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Select the cloud storage provider to store new attachments."
msgstr "เลือกผู้ให้บริการที่จัดเก็บข้อมูลบนคลาวด์เพื่อจัดเก็บสิ่งที่แนบมาใหม่"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_ir_attachment__type
msgid "Type"
msgstr "ประเภท"

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "You are not allowed to upload file to the cloud storage"
msgstr "คุณไม่ได้รับอนุญาตให้อัพโหลดไฟล์ไปยังที่เก็บข้อมูลบนคลาวด์"

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_ir_attachment__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"คุณสามารถอัปโหลดไฟล์จากคอมพิวเตอร์ของคุณ "
"หรือคัดลอก/วางอินเตอร์เน็ตลิงก์ไปยังไฟล์ของคุณ"

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid ""
"webclient can upload files larger than the minimum file size\n"
"        (in bytes) as url attachments to the server and then upload the file to\n"
"        the cloud storage."
msgstr ""
"เว็บไคลเอนท์สามารถอัปโหลดไฟล์ที่มีขนาดใหญ่กว่าขนาดไฟล์ขั้นต่ำ\n"
"        (ในหน่วยไบต์) ในรูปแบบไฟล์แนบ url ไปยังเซิร์ฟเวอร์ แล้วอัปโหลดไฟล์ไปยังที่เก็บข้อมูล\n"
"        บนคลาวด์ได้"
