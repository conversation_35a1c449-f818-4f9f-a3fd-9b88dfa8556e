# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cloud_storage
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil <PERSON>do<PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_attachment
msgid "Attachment"
msgstr "Pièce jointe"

#. module: cloud_storage
#: model:ir.model.fields.selection,name:cloud_storage.selection__ir_attachment__type__cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage"
msgstr "Cloud Storage"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_provider
msgid "Cloud Storage Provider for new attachments"
msgstr "Fournisseur Cloud Storage pour les nouvelles pièces jointes"

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Cloud Storage Settings"
msgstr "Paramètres Cloud Storage"

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/ir_attachment.py:0
msgid "Cloud Storage is not enabled"
msgstr "Cloud Storage n'est pas activé"

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/controllers/attachment.py:0
msgid "Cloud storage configuration has been changed. Please refresh the page."
msgstr "La configuration Cloud Storage a changé. Veuillez actualiser la page."

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "Cloud storage error"
msgstr "Erreur Cloud storage"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: cloud_storage
#: model:ir.model,name:cloud_storage.model_ir_http
msgid "HTTP Routing"
msgstr "Routage HTTP"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid "Minimum File Size (bytes)"
msgstr "Taille minimale du fichier (bytes)"

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Minimum size(bytes) for attachments to be stored in the cloud storage"
msgstr "Taille minimale (octets) des pièces jointes à stocker dans le cloud"

#. module: cloud_storage
#. odoo-python
#: code:addons/cloud_storage/models/res_config_settings.py:0
msgid "Please configure the Cloud Storage before enabling it"
msgstr "Veuillez configurer Cloud Storage avant de l'activer"

#. module: cloud_storage
#: model_terms:ir.ui.view,arch_db:cloud_storage.cloud_storage_config_settings_view_form
msgid "Select the cloud storage provider to store new attachments."
msgstr ""
"Sélectionnez le fournisseur de stockage cloud pour stocker les nouvelles "
"pièces jointes"

#. module: cloud_storage
#: model:ir.model.fields,field_description:cloud_storage.field_ir_attachment__type
msgid "Type"
msgstr "Type"

#. module: cloud_storage
#. odoo-javascript
#: code:addons/cloud_storage/static/src/core/common/attachment_upload_service_patch.js:0
msgid "You are not allowed to upload file to the cloud storage"
msgstr ""
"Vous n'êtes pas autorisé à télécharger des fichiers vers le stockage cloud."

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_ir_attachment__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Vous pouvez soit charger un fichier depuis votre ordinateur, soit "
"copier/coller une adresse internet qui y renvoie."

#. module: cloud_storage
#: model:ir.model.fields,help:cloud_storage.field_res_config_settings__cloud_storage_min_file_size
msgid ""
"webclient can upload files larger than the minimum file size\n"
"        (in bytes) as url attachments to the server and then upload the file to\n"
"        the cloud storage."
msgstr ""
"webclient peut télécharger des fichiers d'une taille supérieure à la taille minimale\n"
"        (en octets) sous forme de pièces jointes vers le serveur, puis télécharger le fichier vers\n"
"        le stockage cloud."
