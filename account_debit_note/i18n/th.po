# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_debit_note
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_move_form_debit
msgid "<span class=\"o_stat_text\">Debit Notes</span>"
msgstr "<span class=\"o_stat_text\">ใบเพิ่มหนี้</span>"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr "เพิ่มโปรแกรมใบเพิ่มหนี้"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Cancel"
msgstr "ยกเลิก"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_journal__debit_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" debit notes made from this journal"
msgstr ""
"ทำเครื่องหมายในช่องนี้หากคุณไม่ต้องการแบ่งปันลำดับเดียวกันสำหรับใบแจ้งหนี้และใบลดหนี้ที่ทำจากสมุดรายวันนี้"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__copy_lines
msgid "Copy Lines"
msgstr "คัดลอกบรรทัด"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__country_code
msgid "Country Code"
msgstr "รหัสประเทศ"

#. module: account_debit_note
#: model:ir.actions.act_window,name:account_debit_note.action_view_account_move_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Create Debit Note"
msgstr "สร้างใบเพิ่มหนี้"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: account_debit_note
#: model:ir.actions.server,name:account_debit_note.action_move_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_filter_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_line_filter_debit
msgid "Debit Note"
msgstr "ใบเพิ่มหนี้"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__date
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Debit Note Date"
msgstr "วันที่ใบเพิ่มหนี้"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/models/account_move.py:0
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_ids
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_invoice_filter_debit
msgid "Debit Notes"
msgstr "ใบเพิ่มหนี้"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_journal__debit_sequence
msgid "Dedicated Debit Note Sequence"
msgstr "ลำดับใบเพิ่มหนี้เฉพาะ"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__id
msgid "ID"
msgstr "ไอดี"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__journal_id
msgid "If empty, uses the journal of the journal entry to be debited."
msgstr "หากว่างเปล่า ให้ใช้สมุดรายวันของรายการสมุดรายวันที่จะเดบิต"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__copy_lines
msgid ""
"In case you need to do corrections for every line, it can be in handy to "
"copy them.  We won't copy them for debit notes from credit notes. "
msgstr ""
"ในกรณีที่คุณจำเป็นต้องแก้ไขทุกบรรทัด คุณสามารถคัดลอกได้อย่างสะดวก "
"เราจะไม่คัดลอกเป็นใบเพิ่มหนี้จากใบลดหนี้"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_journal
msgid "Journal"
msgstr "สมุดบันทึก"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_move
msgid "Journal Entry"
msgstr "รายการสมุดรายวัน"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_type
msgid "Journal Type"
msgstr "ประเภทสมุดรายวัน"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_ids
msgid "Move"
msgstr "ย้าย"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_type
msgid "Move Type"
msgstr "ประเภทการย้าย"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_count
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_count
msgid "Number of Debit Notes"
msgstr "จำนวนใบลดหนี้"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_origin_id
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_origin_id
msgid "Original Invoice Debited"
msgstr "ใบแจ้งหนี้เดบิตต้นฉบับ"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__reason
msgid "Reason"
msgstr "เหตุผล"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"โค้ดประเทศ ISO ในสองตัวอักษร\n"
"คุณสามารถใช้ช่องนี้เพื่อการค้นหาอย่างรวดเร็ว"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,help:account_debit_note.field_account_move__debit_note_ids
msgid "The debit notes created for this invoice"
msgstr "ใบเพิ่มหนี้ที่สร้างขึ้นสำหรับใบแจ้งหนี้นี้"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "This debit note was created from: %s"
msgstr "ใบเพิ่มหนี้นี้ถูกสร้างขึ้นจาก: %s"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_id
msgid "Use Specific Journal"
msgstr "ใช้สมุดรายวันเฉพาะ"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can make a debit note only for a Customer Invoice, a Customer Credit "
"Note, a Vendor Bill or a Vendor Credit Note."
msgstr ""
"คุณสามารถจัดทำใบเพิ่มหนี้ได้เฉพาะในใบแจ้งหนี้ของลูกค้า ใบลดหนี้ของลูกค้า "
"ใบเรียกเก็บเงินของผู้จำหน่าย หรือใบลดหนี้ของผู้จำหน่ายเท่านั้น"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "You can only debit posted moves."
msgstr "คุณสามารถเดบิตเฉพาะการเคลื่อนไหวที่ลงรายการบัญชีแล้วเท่านั้น"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can't make a debit note for an invoice that is already linked to a debit"
" note."
msgstr ""
"คุณไม่สามารถจัดทำใบเพิ่มหนี้สำหรับใบแจ้งหนี้ที่เชื่อมโยงกับใบเพิ่มหนี้แล้วได้"
