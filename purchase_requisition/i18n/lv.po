# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition
# 
# Translators:
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON> <j<PERSON><PERSON><EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>ī<PERSON>ltaje<PERSON>s <<EMAIL>>, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$50"
msgstr "50 $"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$500"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.report,print_report_name:purchase_requisition.action_report_purchase_requisitions
msgid "'Purchase Agreement - %s' % (object.name)"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "02/16/2024"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "12/25/2024"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "2023-09-15"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>From</strong></span>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>to</strong></span>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Contact:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Reference:</strong><br/>"
msgstr "<strong>Reference:</strong><br/>"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction
msgid "Action Needed"
msgstr "Nepieciešama darbība"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__active
msgid "Active"
msgstr "Aktīvs"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_ids
msgid "Activities"
msgstr "Aktivitātes"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitātes izņēmuma noformējums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_state
msgid "Activity State"
msgstr "Aktivitātes stāvoklis"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitātes tipa ikona"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__name
msgid "Agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_type
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__requisition_type
msgid "Agreement Type"
msgstr "Agreement Type"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Agreement Validity"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Agreement Validity:"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__alternative_po_ids
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__alternative_po_ids
msgid "Alternative POs"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternative Purchase Order"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Alternative Warning"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternatives"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "An example of a purchase agreement is a blanket order."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analītiskais sadalījums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_precision
msgid "Analytic Precision"
msgstr "Analītiskā precizitāte"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Archived"
msgstr "Arhivēts"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_attachment_count
msgid "Attachment Count"
msgstr "Pielikumu skaits"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "BO00004"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__blanket_order
msgid "Blanket Order"
msgstr "Blanket Order"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Blanket Orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Buyer"
msgstr "Pircējs"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel"
msgstr "Atcelt"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Cancel Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__cancel
msgid "Cancelled"
msgstr "Atcelts"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Cancelled by the agreement associated to this quotation."
msgstr "Cancelled by the agreement associated to this quotation."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid "Category"
msgstr "Kategorija"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Choose"
msgstr "Izvēlieties"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
msgid "Choose a vendor for alternative PO"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear"
msgstr "Notīrīt"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear Selected"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close"
msgstr "Aizvērt"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__done
msgid "Closed"
msgstr "Slēgts"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Code"
msgstr "Kods"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__company_id
msgid "Company"
msgstr "Uzņēmums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__company_currency_id
msgid "Company Currency"
msgstr "Uzņēmuma valūta"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__price_total_cc
msgid "Company Subtotal"
msgstr "Uznēmuma starpsumma"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Company Total"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Compare Order Lines"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Compare Product Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr "Apstiprināt"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__confirmed
msgid "Confirmed"
msgstr "Apstiprināts"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Mērvienību konvertēšana starp mērvienībām var notikt tikai tad, ja tās "
"pieder vienai un tai pašai kategorijai. Pārrēķins tiks veikts, pamatojoties "
"uz attiecībām."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid "Copy Products"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
msgid "Create Alternative"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid ""
"Create a call for tender by adding alternative requests for quotation to different vendors.\n"
"                            Make your choice by selecting the best combination of lead time, OTD and/or total amount.\n"
"                            By comparing product lines you can also decide to order some products from one vendor and others from another vendor."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Create alternative"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid "Creation Blocked"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__currency_id
msgid "Currency"
msgstr "Valūta"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Date"
msgstr "Datums"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Demo Reference"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__description
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_description_variants
msgid "Description"
msgstr "Apraksts"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Discard"
msgstr "Atmest"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__display_name
msgid "Display Name"
msgstr "Nosaukums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Izplatīšanas analītiskais konts"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Done"
msgstr "Gatavs"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__draft
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Draft"
msgstr "Melnraksts"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_end
msgid "End Date"
msgstr "Beigu datums"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"End date cannot be earlier than start date. Please check dates for "
"agreements: %s"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Expected on"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_follower_ids
msgid "Followers"
msgstr "Sekotāji"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekotāji (Partneri)"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Fonts awesome ikona, piem., fa-tasks"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specific period\n"
"            (e.g. a year) and you order products within this agreement to benefit\n"
"            from the negotiated prices."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Future Activities"
msgstr "Nākotnes aktivitātes"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Grupēt pēc"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__has_alternatives
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_kpis_tree_inherit_purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_tree_inherit_purchase_requisition
msgid "Has Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__has_message
msgid "Has Message"
msgstr "Ir ziņojums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__id
msgid "ID"
msgstr "ID"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona izņēmuma aktivitātes identificēšanai."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ja atzīmēts, jums jāpievērš uzmanība jauniem ziņojumiem."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ja atzīmēts, daži ziņojumi satur piegādes kļūdu."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid ""
"If the chosen vendor or if any of the products in the original PO have a "
"blocking warning then we prevent creation of alternative PO. This is because"
" normally these fields are cleared w/warning message within form view, but "
"we cannot recreate that in this case."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid ""
"If this is checked, the product quantities of the original PO will be copied"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_is_follower
msgid "Is Follower"
msgstr "Ir sekotājs"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Keep Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_uid
msgid "Last Updated by"
msgstr "Pēdējo reizi atjaunoja"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_date
msgid "Last Updated on"
msgstr "Pēdējās izmaiņas"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Late Activities"
msgstr "Pēdējās aktivitātes"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.res_config_settings_view_form_purchase_requisition
msgid "Link RFQs together and compare them"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Link to Existing RfQ"
msgstr ""

#. module: purchase_requisition
#: model:res.groups,name:purchase_requisition.group_purchase_alternatives
msgid "Manage Purchase Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error
msgid "Message Delivery error"
msgstr "Ziņojuma piegādes kļūda"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_ids
msgid "Messages"
msgstr "Ziņojumi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Mitchell Admin"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Cien.\n"
"       <t t-if=\"object.partner_id.parent_id\">\n"
"           <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t> (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>),\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"           <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"       </t>\n"
"       <br><br>\n"
"        Šeit ir jūsu\n"
"       <t t-if=\"object.name\">\n"
"            kredīta atzīme <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">RINV/2021/05/0001</span>\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"            kredītrēķins\n"
"       </t>\n"
"       <t t-if=\"object.invoice_origin\">\n"
"            (ar atsauci: <t t-out=\"object.invoice_origin or ''\">SUB003</t>)\n"
"       </t>\n"
"       <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 143 750,00</span>apmērā\n"
"        no <t t-out=\"object.company_id.name or ''\">Jūsu uzņēmuma</t>.\n"
"       <br><br>\n"
"        Ja jums ir kādi jautājumi, sazinieties ar mums.\n"
"        <t t-if=\"not is_html_empty(object.invoice_user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.invoice_user_id.signature or ''\">--<br>Mitchell admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Manas aktivitātes izpildes termiņš"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "My Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Name, TIN, Email, or Reference"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "New"
msgstr "Jauns"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr "New Agreements"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr "Jauns piedāvājums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nākamās darbības kalendāra pasākums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nākamās aktivitātes beigu termiņš"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_summary
msgid "Next Activity Summary"
msgstr "Nākamās darbības kopsavilkums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_id
msgid "Next Activity Type"
msgstr "Nākamās darbības veids"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Nothing to clear"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of Actions"
msgstr "Darbību skaits"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__order_count
msgid "Number of Orders"
msgstr "Number of Orders"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of errors"
msgstr "Kļūdu skaits"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Ziņojumu, kuriem nepieciešama darbība, skaits"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Ziņojumu, kas satur piegādes kļūdu, skaits"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__order_ids
msgid "Order"
msgstr "Pasūtījums"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__qty_ordered
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Ordered"
msgstr "Pasūtīts"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr "Ordering Date"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Orders"
msgstr "Pasūtījumi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "Origin Po"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__alternative_po_ids
msgid "Other potential purchase orders for purchasing products"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__po_ids
msgid "POs to Confirm"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__product_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Product"
msgstr "Prece"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_id
msgid "Product Unit of Measure"
msgstr "Preces mērvienība"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_product
msgid "Product Variant"
msgstr "Preces variants"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Preces"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__line_ids
msgid "Products to Purchase"
msgstr "Products to Purchase"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__requisition_id
msgid "Purchase Agreement"
msgstr "Iepirkuma pieprasījums"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Agreement:"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr "Pirkuma Līgumi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_res_config_settings__group_purchase_alternatives
msgid "Purchase Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__purchase_group_id
msgid "Purchase Group"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr "Iepirkšanas Pasūtījums"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Pasūtījuma rinda"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Purchase Order Lines"
msgstr "Iepirkumu rindas"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__purchase_ids
msgid "Purchase Orders"
msgstr "Pirkumu pasūtījumi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr "Purchase Orders with requisition"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Reference"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Representative"
msgstr "Pirkuma Pārstāvis"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Purchase Requisition"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_line_id
msgid "Purchase Requisition Line"
msgstr "Purchase Requisition Line"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__purchase_template
msgid "Purchase Template"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Templates"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Quantity"
msgstr "Daudzums"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "RFQ"
msgstr "PPK"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr "RFQs/Orders"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__rating_ids
msgid "Ratings"
msgstr "Reitingi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__reference
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Reference"
msgstr "Reference"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr "Kvotas pieprasījums"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr "Kvotu pieprasījums"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr "Requisition"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Atstatīt uz melnrakstu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_user_id
msgid "Responsible User"
msgstr "Atbildīgie lietotāji"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Īsziņas piegādes kļūda"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr "Meklēt Iepirkumu pieprasījumus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Rādīt visus ierakstus, kuriem nākamais darbības datums ir pirms šodienas"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Some not cleared"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid ""
"Some quantities were not cleared because their status is not a RFQ status."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_start
msgid "Start Date"
msgstr "Sākuma datums"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Start a new purchase agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Statuss"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statuss balstīts uz darbībām\n"
"Kavēts: termiņš jauy ir pagājis\n"
"Šodien: darbības datums ir šodien\n"
"Plānots: nākotnes aktivitātes."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__supplier_info_ids
msgid "Supplier Info"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Piegādātāja cenrādis"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_group
msgid "Technical model to group PO for call to tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Noteikumi un nosacījumi"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "The original PO that this alternative PO is being created for."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"The vendor you have selected or at least one of the products you are copying"
" from the original order has a blocking warning on it and cannot be selected"
" to create an alternative."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "There are no quantities to clear."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"There is already an open blanket order for this supplier. We suggest you "
"complete this open blanket order, instead of creating a new one."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid "This is a blocking warning!\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"To close this purchase requisition, cancel related Requests for Quotation.\n"
"\n"
"Imagine the mess if someone confirms these duplicates: double the order, double the trouble :)"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Today Activities"
msgstr "Šodienas aktivitātes"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Total"
msgstr "Kopā"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Reģistrētās izņēmuma aktivitātes tips."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit"
msgstr "Vienība"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit Price"
msgstr "Cena"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "UoM"
msgstr "Mērv"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__vendor_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Vendor"
msgstr "Piegādātājs"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__purchase_warn_msg
msgid "Warning Messages"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(partner)s:\n"
"%(warning_message)s\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(product)s:\n"
"%(warning_message)s\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Warning for %s"
msgstr "Brīdinājums par %s"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website Messages"
msgstr "Tīmekļa lapas ziņojumi"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website communication history"
msgstr "Tīmekļa lapas komunikācijas vēsture"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "What about the alternative Requests for Quotations?"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__has_alternatives
msgid ""
"Whether or not this purchase order is linked to another purchase order as an"
" alternative."
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_alternative_warning
msgid "Wizard in case PO still has open alternative requests for quotation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_create_alternative
msgid "Wizard to preset values for alternative PO"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You can only delete draft or cancelled requisitions."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot change the Agreement Type or Company of a not draft purchase "
"agreement."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a price."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a quantity."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot confirm agreement '%(agreement)s' because it does not contain any"
" product lines."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot have a negative or unit price of 0 for an already confirmed "
"blanket order."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr "e.g. PO0025"
