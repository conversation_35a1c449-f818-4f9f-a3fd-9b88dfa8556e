<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="calendar.Many2ManyAttendeeExpandable" t-inherit="web.Many2ManyTagsAvatarField" t-inherit-mode="primary">
        <xpath expr="//div[hasclass('many2many_tags_avatar_field_container')]" position="attributes">
            <attribute name="class" remove="mw-100" add="w-100" separator=" "/>
        </xpath>
        <xpath expr="//TagsList" position="attributes">
            <attribute name="visibleItemsLimit">state.expanded ? tags.length : 5</attribute>
        </xpath>
        <xpath expr="//TagsList" position="before">
            <t t-if="tags.length > 5">
                <div class="d-flex w-100">
                    <div class="flex-grow-1">
                        <strong><t t-out="attendeesCount"/> attendees</strong>
                        <br/><t t-out="acceptedCount"/> accepted
                        <br/><t t-out="declinedCount"/> declined
                        <br/><t t-out="uncertainCount"/> uncertain
                    </div>
                    <i role="button" t-on-click="onExpanderClick" t-att-class="state.expanded ? 'oi oi-chevron-up' : 'oi oi-chevron-down'"></i>
                </div>
            </t>
        </xpath>
        <xpath expr="//TagsList" position="replace">
            <t t-if="!state.expanded &amp;&amp; tags.length > 10"> </t>
            <t t-else="">$0</t>
        </xpath>
    </t>

</templates>
