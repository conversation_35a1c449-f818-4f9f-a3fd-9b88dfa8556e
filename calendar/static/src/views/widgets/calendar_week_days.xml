<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="calendar.WeekDays">
        <div class="o_recurrent_weekdays mb-2 d-flex">
        <t t-foreach="weekdays" t-as="day" t-key="day">
                <div t-attf-class="o_calendar_week_days_rounded {{data[day] ? 'text-bg-action' : 'btn-secondary'}}"
                    t-on-click="this.props.readonly? () => {} : () => this.onChange(day)">
                    <t t-esc="props.record.fields[day].string[0]"/>
                </div>
            </t>
        </div>
    </t>
</templates>
