<?xml version="1.0"?>
<odoo>
    <data>
        <!-- Update Preferences form !-->
        <record id="res_users_form_view_calendar_default_privacy" model="ir.ui.view">
            <field name="name">res.users.preferences.form.inherit</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form_simple_modif"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='tz']" position="after">
                    <field name="calendar_default_privacy" readonly="0" string="Calendar Default Privacy" invisible="share"/>
                </xpath>
            </field>
        </record>
        <!-- Update User form !-->
        <record id="res_users_form_view" model="ir.ui.view">
            <field name="name">res.users.form.calendar</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='signature']" position="after">
                    <field name="calendar_default_privacy" readonly="0" string="Calendar Default Privacy" invisible="share"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
