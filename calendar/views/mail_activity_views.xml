<?xml version="1.0"?>
<odoo>

    <record id="mail_activity_view_form_popup" model="ir.ui.view">
        <field name="name">mail.activity.form.inherit.calendar</field>
        <field name="model">mail.activity</field>
        <field name="inherit_id" ref="mail.mail_activity_view_form_popup"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='date_deadline']" position="attributes">
                  <attribute name="invisible">activity_category == 'meeting'</attribute>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="attributes">
                  <attribute name="invisible">activity_category == 'meeting'</attribute>
            </xpath>
            <xpath expr="//button[@id='mail_activity_schedule']" position="attributes">
                  <attribute name="invisible">activity_category in ['meeting', 'phonecall'] or id</attribute>
            </xpath>
            <xpath expr="//button[@id='mail_activity_save']" position="attributes">
                  <attribute name="invisible">activity_category != 'phonecall' and not id</attribute>
            </xpath>
            <xpath expr="//button[@name='action_done']" position="attributes">
                  <attribute name="invisible">activity_category == 'meeting' or chaining_type == 'trigger'</attribute>
            </xpath>
            <xpath expr="//field[@name='note']" position="attributes">
                  <attribute name="invisible">activity_category == 'meeting'</attribute>
            </xpath>
            <xpath expr="//button[@name='action_close_dialog']" position="before">
                  <field name="calendar_event_id" invisible="1" />
                  <button string="Open Calendar"
                        close="1"
                        invisible="activity_category not in ['meeting', 'phonecall'] or calendar_event_id"
                        name="action_create_calendar_event"
                        type="object"
                        class="btn-primary"/>
            </xpath>
        </field>
    </record>

</odoo>
