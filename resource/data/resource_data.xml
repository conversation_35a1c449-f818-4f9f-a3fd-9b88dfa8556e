<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="resource_calendar_std" model="resource.calendar">
            <field name="name">Standard 40 hours/week</field>
            <field name="company_id" ref="base.main_company"/>
            <field name="full_time_required_hours">40</field>
        </record>

        <record id="base.main_company" model="res.company">
            <field name="resource_calendar_id" ref="resource_calendar_std"/>
        </record>

        <function
            model="res.company"
            name="_init_data_resource_calendar"
            eval="[]"/>
    </data>
</odoo>
