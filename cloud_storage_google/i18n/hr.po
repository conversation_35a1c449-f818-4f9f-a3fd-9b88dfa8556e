# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cloud_storage_google
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Bo<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: cloud_storage_google
#: model:ir.model,name:cloud_storage_google.model_ir_attachment
msgid "Attachment"
msgstr "Prilog"

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_provider
msgid "Cloud Storage Provider for new attachments"
msgstr ""

#. module: cloud_storage_google
#: model:ir.model,name:cloud_storage_google.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguracijske postavke"

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_google_bucket_name
msgid "Google Bucket Name"
msgstr ""

#. module: cloud_storage_google
#: model:ir.model.fields.selection,name:cloud_storage_google.selection__res_config_settings__cloud_storage_provider__google
msgid "Google Cloud Storage"
msgstr ""

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_google_account_info
msgid "Google Service Account Info"
msgstr ""

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_google_service_account_key
msgid "Google Service Account Key"
msgstr ""

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"Some Google attachments are in use, please migrate cloud storages before "
"disable the provider"
msgstr ""

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"The account info is not allowed to download blobs from the bucket.\n"
"%s"
msgstr ""

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"The account info is not allowed to set the bucket's CORS.\n"
"%s"
msgstr ""

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"The account info is not allowed to upload blobs to the bucket.\n"
"%s"
msgstr ""
