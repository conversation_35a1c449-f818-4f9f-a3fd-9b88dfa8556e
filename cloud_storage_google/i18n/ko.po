# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cloud_storage_google
# 
# Translators:
# Wil Odo<PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: cloud_storage_google
#: model:ir.model,name:cloud_storage_google.model_ir_attachment
msgid "Attachment"
msgstr "첨부 파일"

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_provider
msgid "Cloud Storage Provider for new attachments"
msgstr "새 첨부파일을 위한 클라우드 스토리지 제공업체"

#. module: cloud_storage_google
#: model:ir.model,name:cloud_storage_google.model_res_config_settings
msgid "Config Settings"
msgstr "환경설정"

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_google_bucket_name
msgid "Google Bucket Name"
msgstr "Google 버킷 이름"

#. module: cloud_storage_google
#: model:ir.model.fields.selection,name:cloud_storage_google.selection__res_config_settings__cloud_storage_provider__google
msgid "Google Cloud Storage"
msgstr "Google 클라우드 스토리지"

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_google_account_info
msgid "Google Service Account Info"
msgstr "Google 서비스 계정 정보"

#. module: cloud_storage_google
#: model:ir.model.fields,field_description:cloud_storage_google.field_res_config_settings__cloud_storage_google_service_account_key
msgid "Google Service Account Key"
msgstr "Google 서비스 계정 키"

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"Some Google attachments are in use, please migrate cloud storages before "
"disable the provider"
msgstr "현재 일부 Google 첨부파일이 사용 중입니다. 제공업체를 비활성화하기 전에 클라우드 저장소를 마이그레이션하세요."

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"The account info is not allowed to download blobs from the bucket.\n"
"%s"
msgstr ""
"계정 정보로 버킷에서 Blob을 다운로드할 수 없습니다.\n"
"%s"

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"The account info is not allowed to set the bucket's CORS.\n"
"%s"
msgstr ""
"계정 정보로는 버킷의 CORS를 설정할 수 없습니다.\n"
"%s"

#. module: cloud_storage_google
#. odoo-python
#: code:addons/cloud_storage_google/models/res_config_settings.py:0
msgid ""
"The account info is not allowed to upload blobs to the bucket.\n"
"%s"
msgstr ""
"계정 정보로 버킷에서 Blob을 업로드할 수 없습니다.\n"
"%s"
