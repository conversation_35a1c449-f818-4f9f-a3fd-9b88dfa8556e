<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.mass.mailing</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="60"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="inside">
                    <app data-string="Email Marketing" string="Email Marketing" name="mass_mailing" groups="mass_mailing.group_mass_mailing_user">
                        <block title="Email Marketing" name="managa_mail_campaigns_setting_container">
                            <setting title="This tool is advised if your marketing campaign is composed of several emails." help="Manage mass mailing campaigns">
                                <field name="group_mass_mailing_campaign"/>
                            </setting>
                            <setting name="contact_naming" title="Contact Naming" help="Separate Mailing Contact Names into two fields">
                                <field name="mass_mailing_split_contact_name"/>
                            </setting>
                            <setting name="allow_blacklist_setting_container" title="Allow the recipient to manage themselves their state in the blacklist via the unsubscription page. If the option is active, the 'Blacklist Me' button is hidden on the unsubscription page. The 'come Back' button will always be visible in any case to allow leads and partners to re-subscribe." help="Allow recipients to blacklist themselves">
                                <field name="show_blacklist_buttons"/>
                            </setting>
                            <setting name="mass_mailing_reports_setting_container" title="Send a report to the mailing responsible one day after the mailing has been sent." help="Check how well your mailing is doing a day after it has been sent">
                                <field name="mass_mailing_reports"/>
                            </setting>
                            <setting name="dedicated_server_setting_container" title="Use a specific mail server in priority. Otherwise Odoo relies on the first outgoing mail server available (based on their sequencing) as it does for normal mails." help="Pick a dedicated outgoing mail server for your mass mailings">
                                <field name="mass_mailing_outgoing_mail_server"/>
                                <div class="content-group" invisible="not mass_mailing_outgoing_mail_server">
                                    <div class="mt16">
                                        <field name="mass_mailing_mail_server_id" options="{'no_create': True, 'no_open': True}"
                                                placeholder="Default Server"/>
                                    </div>
                                    <div class="mt8">
                                        <button type="action" name="base.action_ir_mail_server_list" string="Configure Outgoing Mail Servers" icon="oi-arrow-right" class="oe_link"/>
                                    </div>
                                </div>
                            </setting>
                        </block>
                    </app>
                </xpath>
            </field>
        </record>

        <record id="action_mass_mailing_configuration" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'mass_mailing', 'bin_size': False}</field>
        </record>
</odoo>
