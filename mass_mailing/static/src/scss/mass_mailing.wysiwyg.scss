.o_mass_mailing_iframe body {
    .modal:not(.o_technical_modal) {
        top: 0 !important;
        // set z-index so customize options visible on dialog.
        z-index: $o-we-overlay-zindex - 1;
        // just for fake backdrop effect
        background-color: rgba(66, 66, 66, 0.4);
    }

    .oe_structure .oe_drop_zone {

        &:first-child {
            transform: translateY(50%);
        }
        &:last-child {
            transform: translateY(-50%);
        }
    }
}

@mixin email_designer_btn {
    align-items: center;
    width: 24px;
    height: 24px;
    background-color: #337ab7 !important;
    border: 1px solid #2e6da4 !important;
    border-radius: 4px;
    padding: 0;
    margin-left: 5px;
}

.o_field_mass_mailing_html {
    .wysiwyg_iframe.o_iframe.has_snippets_sidebar {
        border: none,
    }
    .o_mass_mailing_iframe_ancestor_fullscreen {
        // Prevent the website snippets sidebar from overlapping the template
        .wysiwyg_iframe.o_iframe.has_snippets_sidebar {
            padding-right: $o-we-sidebar-width !important;
        }
    }
}

.o_field_mass_mailing_html #oe_snippets {
    position: sticky;
    top: 0;
    height: 100%;
    z-index: $zindex-dropdown - 1;
    display: none;

    .o_we_external_history_buttons {
        display: flex;
        margin: auto 130px auto auto;

        .btn {
            @include email_designer_btn;
        }
    }

    .email_designer_top_actions {
        display: flex;
        margin: auto 9px auto auto;

        .btn {
            @include email_designer_btn;
        }
        .o_fullscreen_btn img {
            margin: auto;
        }
    }
    &.o_loaded {
        display: flex;
        transform: translateX(0);

        > .o_we_customize_panel .oe-toolbar {
            grid-template-areas:
                "typo typo style style colors"
                "size align list list link"
                "ai translate translate . ."
                "options options options options options"
                "options2 options2 options2 options2 options2"
                "options3 options3 options3 options3 options3";

            #style.dropup, #font-size.dropup {
                > .dropdown-menu {
                    overflow-y: scroll;
                    max-height: 200px;
                }
            }

            #translate.dropup {
                > .dropdown-menu {
                    max-height: 250px;
                }
            }

            // Popper.js manipulates transform and position of the dropdown. In
            // mass_mailing, its calculations are wrong. These !important rules
            // removes the wrong values automatically generated by Popper.
            #justify , #style.dropup, #font-size.dropup {
                > .dropdown-menu {
                    transform: unset !important;
                    bottom: 100% !important;
                    top: unset !important;
                }
            }
            #font-size > button {

                &:focus-within input {
                    padding: 0 !important;
                    width: calc(2ch + #{$o-we-sidebar-content-field-clickable-spacing * 2} - #{$o-we-sidebar-content-field-border-width * 2});
                }
                input {
                    padding: 0;
                    text-align: center;
                    width: calc(2ch + #{$o-we-sidebar-content-field-clickable-spacing * 2});
                }
            }
        }
    }

    .o_we_customize_panel we-input.o_we_user_value_widget > div {
        width: 69px;  // show 3 decimal places
    }
}

.o_mailing_mailing_view_form_full_width_view .o-mail-Form-chatter.o-aside {
    // When the chatter is aside, it pushes the content of the iframe
    // such that in some screen resolutions, you will see the mobile
    // view. We reduce the width of the chatter to avoid this.
    width: $o-mail-Chatter-minWidth - $o-we-sidebar-width;
}
