<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="10.577" height="8.581" x="0" y="0"/>
    <linearGradient id="linearGradient-3" x1="72.875%" x2="40.332%" y1="46.271%" y2="33.176%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="88.517%" x2="50%" y1="38.751%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-5" width="9.615" height="8.581" x="0" y="0"/>
    <linearGradient id="linearGradient-7" x1="72.875%" x2="40.332%" y1="45.488%" y2="29.644%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-8" x1="88.517%" x2="50%" y1="36.389%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-9" width="11.538" height="8.581" x="0" y="0"/>
    <linearGradient id="linearGradient-11" x1="72.875%" x2="40.332%" y1="46.866%" y2="35.864%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-12" x1="88.517%" x2="50%" y1="40.548%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-13" width="10.577" height="8.581" x="0" y="0"/>
    <rect id="path-15" width="22" height="2" x="17" y="0"/>
    <filter id="filter-16" width="104.5%" height="200%" x="-2.3%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-17" width="14" height="1" x="21" y="5"/>
    <filter id="filter-18" width="107.1%" height="300%" x="-3.6%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_references">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(14 20)">
        <g class="image_1_border" transform="translate(30 11)">
          <rect width="11" height="9" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.212 .21)">
            <mask id="mask-2" fill="#fff">
              <use xlink:href="#path-1"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
            <ellipse cx="8.144" cy="2.198" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="1.587" ry="1.57"/>
            <ellipse cx="10.683" cy="9.419" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-2)" rx="4.971" ry="3.14"/>
            <ellipse cx=".106" cy="9.523" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-2)" rx="7.933" ry="4.919"/>
          </g>
          <path fill="#FFF" d="M11 0v9H0V0h11zm-1 1H1v7h9V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(45 11)">
          <rect width="10" height="9" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.192 .21)">
            <mask id="mask-6" fill="#fff">
              <use xlink:href="#path-5"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
            <ellipse cx="7.404" cy="2.198" fill="#F3EC60" class="oval" mask="url(#mask-6)" rx="1.442" ry="1.57"/>
            <ellipse cx="9.712" cy="9.419" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-6)" rx="4.519" ry="3.14"/>
            <ellipse cx=".096" cy="9.523" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-6)" rx="7.212" ry="4.919"/>
          </g>
          <path fill="#FFF" d="M10 0v9H0V0h10zM9 1H1v7h8V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(0 11)">
          <rect width="12" height="9" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.23 .21)">
            <mask id="mask-10" fill="#fff">
              <use xlink:href="#path-9"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-9"/>
            <ellipse cx="8.885" cy="2.198" fill="#F3EC60" class="oval" mask="url(#mask-10)" rx="1.731" ry="1.57"/>
            <ellipse cx="11.654" cy="9.419" fill="url(#linearGradient-11)" class="oval" mask="url(#mask-10)" rx="5.423" ry="3.14"/>
            <ellipse cx=".115" cy="9.523" fill="url(#linearGradient-12)" class="oval" mask="url(#mask-10)" rx="8.654" ry="4.919"/>
          </g>
          <path fill="#FFF" d="M12 0v9H0V0h12zm-1 1H1v7h10V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(15 11)">
          <rect width="11" height="9" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.212 .21)">
            <mask id="mask-14" fill="#fff">
              <use xlink:href="#path-13"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-13"/>
            <ellipse cx="8.144" cy="2.198" fill="#F3EC60" class="oval" mask="url(#mask-14)" rx="1.587" ry="1.57"/>
            <ellipse cx="10.683" cy="9.419" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-14)" rx="4.971" ry="3.14"/>
            <ellipse cx=".106" cy="9.523" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-14)" rx="7.933" ry="4.919"/>
          </g>
          <path fill="#FFF" d="M11 0v9H0V0h11zm-1 1H1v7h9V1z" class="rectangle_2"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-16)" xlink:href="#path-15"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-15"/>
        </g>
        <g class="rectangle_copy">
          <use fill="#000" filter="url(#filter-18)" xlink:href="#path-17"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-17"/>
        </g>
      </g>
    </g>
  </g>
</svg>
