<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">
    <!-- Re-render lists form as no reload is done on portal page -->
    <t t-name="mass_mailing.portal.list_form_content">
        <p t-if="listsMember.length">
            Choose your mailing subscriptions
        </p>
        <ul id="o_mailing_subscription_form_lists"
            t-if="listsMember.length"
            class="list-group">
            <li t-foreach="listsMember"
                t-as="mailing_list"
                t-key="mailing_list_key"
                class="list-group-item d-flex align-items-center">
                <input type="checkbox"
                       name="mailing_list_ids"
                       t-att-data-member="1"
                       t-att-value="mailing_list['id']"
                       t-att-checked="mailing_list['opt_out'] ? undefined : 'checked'"
                       t-att-title="mailing_list['name']"/>
                <span class="ms-1" t-out="mailing_list['name']"/>
                <span class="o_mailing_list_unsubscribed ms-auto">
                    <t t-if="mailing_list['opt_out']">
                        Not subscribed
                    </t>
                    <t t-else="">
                        Subscribed
                    </t>
                </span>
            </li>
        </ul>
        <p t-if="listsProposal.length">
            You may also be interested in
        </p>
        <ul id="o_mailing_subscription_form_lists_additional"
            t-if="listsProposal.length"
            class="list-group">
            <li t-foreach="listsProposal"
                t-as="mailing_list"
                t-key="mailing_list_key"
                class="list-group-item">
                <input type="checkbox"
                       name="mailing_list_ids"
                       t-att-value="mailing_list['id']"
                       t-att-title="mailing_list['name']"/>
                <span class="ms-1" t-out="mailing_list['name']"/>
            </li>
        </ul>
    </t>

    <!-- Re-render lists (in readonly mode) as no reload is done on portal page -->
    <t t-name="mass_mailing.portal.list_form_content_readonly">
        <p>
            Your email is currently <strong>in our block list</strong>.
            <t t-if="listsOptin.length">
                You will not receive any news from those mailing lists you are a member of:
            </t>
            <t t-else="">
                You will not hear from us anymore.
            </t>
        </p>
        <ul class="list-group mb-4"
            t-if="listsOptin.length">
            <t t-foreach="listsOptin"
               t-as="mailing_list"
               t-key="mailing_list_key">
                <li class="list-group-item d-flex align-items-center">
                    <strong t-out="mailing_list['name']"/>
                </li>
            </t>
        </ul>
    </t>

    <!-- Post action informative span -->
    <t t-name="mass_mailing.portal.list_form_update_info">
        <t t-if="infoKey == 'subscription_updated'">
            <i class="fa fa-check me-1"/>
            <span>Membership updated</span>
        </t>
        <t t-else="">
            <i class="fa fa-exclamation-triangle me-1"/>
            <span>An error occurred. Please retry later.</span>
        </t>
    </t>
</templates>
