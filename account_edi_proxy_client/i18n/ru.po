# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_proxy_client
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-20 14:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"A user already exists with theses credentials on our server. Please check "
"your information."
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "Учетная запись прокси-пользователя EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_res_company__account_edi_proxy_client_ids
msgid "Account Edi Proxy Client"
msgstr "Учетная запись Edi Proxy Client"

#. module: account_edi_proxy_client
#: model_terms:ir.ui.view,arch_db:account_edi_proxy_client.view_form_account_edi_proxy_client_user
msgid "Account Journal"
msgstr "Журнал счетов"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__active
msgid "Active"
msgstr "Активный"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__company_id
msgid "Company"
msgstr "Компания"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_uid
msgid "Created by"
msgstr "Создано"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_date
msgid "Created on"
msgstr "Создано"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_certificate_key
msgid "Cryptographic Keys"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__demo
msgid "Demo mode"
msgstr "Демонстрационный режим"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: account_edi_proxy_client
#: model:ir.actions.act_window,name:account_edi_proxy_client.action_tree_account_edi_proxy_client_user
msgid "EDI Proxy User"
msgstr "Пользователь прокси-сервера EDI"

#. module: account_edi_proxy_client
#: model:ir.ui.menu,name:account_edi_proxy_client.menu_account_proxy_client_user
msgid "EDI Proxy Users"
msgstr "Пользователи прокси-сервера EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_mode
msgid "EDI operating mode"
msgstr "Режим работы EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "Edi Identification"
msgstr "Идентификация Edi"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id
msgid "ID"
msgstr "ID"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id_client
msgid "Id Client"
msgstr "Id клиента"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_id
msgid "Private Key"
msgstr "Приватный ключ"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__prod
msgid "Production mode"
msgstr "Режим производства"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr "Тип прокси"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__refresh_token
msgid "Refresh Token"
msgstr "Обновить токен"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__test
msgid "Test mode"
msgstr "Тестовый режим"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_id
msgid "The key to encrypt all the user's data"
msgstr "Ключ для шифрования всех данных пользователя"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr ""
"Уникальный идентификатор, идентифицирующий этого пользователя, обычно vat"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %(url)s. %(error_message)s"
msgstr ""
"Url, который запрашивала эта служба, вернул ошибку. Url, с которым он "
"пытался связаться, был %(url)s. %(error_message)s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s"
msgstr ""
"Url, запрошенный этой службой, вернул ошибку. Url, с которым он пытался "
"связаться, был %s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service tried to contact does not exist. The url was “%s”"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_company_proxy
msgid "This company has an active user already created for this EDI type"
msgstr ""
"У этой компании уже есть активный пользователь, созданный для данного типа "
"EDI"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_edi_identification
msgid "This edi identification is already assigned to an active user"
msgstr "Этот идентификатор edi уже назначен активному пользователю"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_id_client
msgid "This id_client is already used on another user."
msgstr "Этот id_client уже используется для другого пользователя."
