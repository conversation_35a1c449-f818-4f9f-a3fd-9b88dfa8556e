# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "%(name)s Sequence repair"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "%(product)s: Insufficient Quantity To Repair"
msgstr ""

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid "('Repair Order - %s' % (object.name))"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Recycle</i>)"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>Odstrani</i>)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'New' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'Repaired' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Confirmed</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Under Repair</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Done:</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repair Parts:</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">To Do:</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span>Orders</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<span>Repair Order #</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Customer:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Responsible:</strong>"
msgstr "<strong>Odgovoren:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Status:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Potreben je ukrep"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Izjema pri oznaki aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Stanje aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona vrste dejavnosti"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_order_view_activity
msgid "Activity view"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__add
msgid "Add"
msgstr "Dodaj"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add a line"
msgstr "Dodaj postavko"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_dest_id
msgid "Added Parts Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__after
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "After"
msgstr "Po"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "All"
msgstr "Vse"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_available
msgid "All Parts are available"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__allowed_lot_ids
msgid "Allowed Lot"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__reserve_visible
msgid "Allowed to Reserve Production"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_late
msgid "Any Part is late"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "Število prilog"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__available
msgid "Available"
msgstr "Razpoložljivo"

#. module: repair
#. odoo-javascript
#: code:addons/repair/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__before
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Before"
msgstr "Prej"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "BoM Components"
msgstr "Sestavni deli kosovnice"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "Preklic popravila"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Cancelled"
msgstr "Preklicano"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Catalog"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "Kategorija"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Check availability"
msgstr "Preveri razpoložljivost"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "Barvni indeks"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Podjetje"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Source Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability
msgid "Component Status"
msgstr ""

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Configuration"
msgstr "Nastavitve"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Potrdi popravilo"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "Potrjeno"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "Pretvorba med enotami mere je možna samo v isti kategoriji."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Quotation"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields,field_description:repair.field_product_product__create_repair
#: model:ir.model.fields,field_description:repair.field_product_template__create_repair
msgid "Create Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__is_repairable
msgid "Create Repair Orders from Returns"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_product_product__create_repair
#: model:ir.model.fields,help:repair.field_product_template__create_repair
msgid ""
"Create a linked Repair Order on Sale Order confirmation of this product."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "Ustvari novo oznako"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "Stranka"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__search_date_category
msgid "Date Category"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Demand"
msgstr "Povpraševanje"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Description"
msgstr "Opis"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Details"
msgstr "Podrobnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Done"
msgstr "Opravljeno"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "Konec popravila"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Exp %s"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__expected
msgid "Expected"
msgstr "Pričakovano"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Sledilci"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledilci (partnerji)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font izjemna ikona npr. fa-tasks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"For some of the parts, there is a difference between the initial demand and "
"the actual quantity that was used. Are you sure you want to confirm ?"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecast Report"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecasted"
msgstr "Napovedano"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Bodoče aktivnosti"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Združi po"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "Ima sporočilo"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_uncomplete_moves
msgid "Has Uncomplete Moves"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona za označevanje izjemne aktivnosti."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Če je označeno, zahtevajo nova sporočila vašo pozornost."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Če je označeno, nekatera sporočila vsebujejo napako pri dostavi."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__under_warranty
msgid ""
"If ticked, the sales price will be set to 0 for all products transferred "
"from the repair order."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__is_repairable
msgid ""
"If ticked, you will be able to directly create repair orders from a return."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__in_repair_count
msgid "In repair count"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "In the Repair Order"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Interne opombe"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Inventory Move"
msgstr "Premik zaloge"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Je sledilec"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__is_repairable
msgid "Is Repairable"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Jane Smith"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "John Doe"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "L12345"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Laptop"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__late
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late"
msgstr "Zamuja"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Aktivnosti z zamudo"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_availability
msgid ""
"Latest parts availability status for this RO. If green, then the RO's "
"readiness status is ready."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Lokacija"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Locations"
msgstr "Lokacije"

#. module: repair
#: model:ir.model,name:repair.model_stock_lot
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Lot/serijska številka"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "Napaka pri dostavi sporočila"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Sporočila"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Miscellaneous"
msgstr "Razno"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Moj rok dejavnosti"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "New"
msgstr "Novo"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Naslednji dogodek v koledarju"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Rok naslednje aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Povzetek naslednje aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Tip naslednje aktivnosti"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "Običajno"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Not Available"
msgstr "Ni na voljo"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"Note that the warehouses of the return and repair locations don't match!"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Število aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_late
msgid "Number of Late Repair Orders"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_confirmed
msgid "Number of Repair Orders Confirmed"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_under_repair
msgid "Number of Repair Orders Under Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_ready
msgid "Number of Repair Orders to Process"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "Število napak"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Število sporočil, ki zahtevajo ukrepanje"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Število sporočil, ki niso bila pravilno dostavljena."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__nbr_repairs
msgid "Number of repairs linked to this picking"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Open"
msgstr "Odprto"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_type_id
msgid "Operation Type"
msgstr "Tip operacije"

#. module: repair
#. odoo-python
#: code:addons/repair/models/product.py:0
#: code:addons/repair/models/repair.py:0
msgid "Operation not supported"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Operacije"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_order_menu
msgid "Orders"
msgstr "Naročila"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_picking_type_menu
msgid "Overview"
msgstr "Pregled"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_ids
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability_state
msgid "Parts Availability State"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Pending"
msgstr "Nerešeno"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_ids
msgid "Picking Product"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_stock_picking_type
msgid "Picking Type"
msgstr "Tip zbirnika"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "Prioriteta"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__procurement_group_id
msgid "Procurement Group"
msgstr "Oskrbovalna skupina"

#. module: repair
#: model:ir.model,name:repair.model_product_template
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Izdelek"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product A"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product B"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_product_product__product_catalog_product_is_in_repair
msgid "Product Catalog Product Is In Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_dest_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_dest_id
msgid "Product Destination Location"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "Premiki proizvoda"

#. module: repair
#: model:ir.model,name:repair.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Količina"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_src_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_src_id
msgid "Product Source Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "Sledenje izdelkom"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Merska enota izdelka"

#. module: repair
#: model:ir.model,name:repair.model_product_product
msgid "Product Variant"
msgstr "Različica izdelka"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_product
msgid "Product Variants"
msgstr "Različice izdelka"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Proizvod za popravilo"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_template
msgid "Products"
msgstr "Izdelki"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "Vsi popravljeni proizvodi pripadajo temu lotu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_properties
msgid "Properties"
msgstr "Lastnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "Količina"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "RO123456"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__rating_ids
msgid "Ratings"
msgstr "Ocene"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready"
msgstr "Pripravljeno"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__recycle
msgid "Recycle"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid "Recycle Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__recycle_location_id
msgid "Recycled Parts Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__remove
msgid "Remove"
msgstr "Odstrani"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_remove_location_dest_id
msgid "Remove Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_location_id
msgid "Removed Parts Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_picking__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
#: model:ir.model.fields.selection,name:repair.selection__stock_picking_type__code__repair_operation
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
msgid "Repair"
msgstr "Popravila"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_mto_pull_id
msgid "Repair MTO Rule"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_type_id
msgid "Repair Operation Type"
msgstr ""

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
#: model:ir.model,name:repair.model_repair_order
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "Servisni nalog"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_count
msgid "Repair Order(s)"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/sale_order.py:0
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.actions.act_window,name:repair.action_picking_repair
#: model:ir.actions.act_window,name:repair.action_picking_repair_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_form
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_line_ids
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "Nalog za popravila"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr ""

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_picking_type_kanban
msgid "Repair Overview"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__repair_properties_definition
msgid "Repair Properties"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "Sklic servisnega naloga"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_request
msgid "Repair Request"
msgstr ""

#. module: repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Repair must be under repair in order to end reparation."
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_lot.py:0
msgid "Repair orders of %s"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_part_count
msgid "Repair part count"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repaired"
msgstr "Popravljeno"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repaired_count
msgid "Repaired count"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
#: model:stock.picking.type,name:repair.picking_type_warehouse0_repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_sale_order_form_inherit_repair
msgid "Repairs"
msgstr "Popravila"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Servisni nalogi"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr ""

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Reporting"
msgstr "Poročanje"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "Odgovoren"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni uporabnik"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_id
msgid "Return"
msgstr "Vrni"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__picking_id
msgid "Return Order from which the product to be repaired comes from."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__return_type_of_ids
msgid "Return Type Of"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_returned
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Returned"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Napaka pri dostavi SMS "

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line"
msgstr "Postavka prodajnega naloga"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__repair_request
msgid "Sale Order Line Description."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line from which the Repair Order comes from."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the Repair Order comes from."
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: repair
#: model:ir.model,name:repair.model_sale_order_line
msgid "Sales Order Line"
msgstr "Postavka naročila"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "Načrtovani datum"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "Iskanje servisnih nalogov"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Serial number is required for product to repair : %s"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Nastavi kot osnutek"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Prikaži vse zapise z datumom naslednje aktivnosti pred današnjim datumom."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "Začni popravilo"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Status"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na osnovi aktivnosti\n"
"Zapadel: Rok je že prekoračen\n"
"Danes: Datum aktivnosti je danes\n"
"Planirano: Bodoče aktivnosti."

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Premik zaloge"

#. module: repair
#: model:ir.model,name:repair.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Poročilo o polnjenju zalog"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "Naziv oznake"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr ""

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "Ključne besede"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "The day after tomorrow"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "This is a repair note."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_dest_id
msgid ""
"This is the default destination location for the product to be repaired in "
"repair orders with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid ""
"This is the default recycle destination location when you create a repair "
"order with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_remove_location_dest_id
msgid ""
"This is the default remove destination location when you create a repair "
"order with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_src_id
msgid ""
"This is the default source location for the product to be repaired in repair"
" orders with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid ""
"This is the location where the components of product to repair is located."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_location_src_id
msgid "This is the location where the product to repair is located."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_location_id
#: model:ir.model.fields,help:repair.field_repair_order__recycle_location_id
msgid "This is the location where the repair parts are located."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_dest_id
#: model:ir.model.fields,help:repair.field_repair_order__product_location_dest_id
msgid "This is the location where the repaired product is located."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "To Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__today
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today"
msgstr "Danes"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Tomorrow"
msgstr "Jutri"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Poročilo o sledljivosti"

#. module: repair
#: model:ir.model,name:repair.model_stock_picking
msgid "Transfer"
msgstr "Prenos"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__is_returned
msgid ""
"True if this repair is linked to a Return Order and the order is 'Done'. "
"False otherwise."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_line_type
msgid "Type"
msgstr "Tip"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Tip operacije"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Vrsta dejavnosti izjeme na zapisu. "

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Under Repair"
msgstr "V popravilu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__under_warranty
msgid "Under Warranty"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "Enota mere"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Units"
msgstr "Enot"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Unreserve"
msgstr "Umik rezervacije"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "Nujno"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Used"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_stock_warehouse
msgid "Warehouse"
msgstr "Skladišče"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Warning"
msgstr "Opozorilo"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Sporočila iz spletne strani"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "Kronologija komunikacij spletne strani"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Yesterday"
msgstr "Včeraj"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You can not enter negative quantities."
msgstr "Ne morete vnesti negativnih količin."

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You cannot cancel a Repair Order that's already been completed"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You cannot create a quotation for a repair order that is already linked to an existing sale order.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You need to define a customer for a repair order in order to create an associated quotation.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr ""

#. module: repair
#: model:ir.actions.server,name:repair.action_repair_overview
msgid "stock.repair.type.overview"
msgstr ""
