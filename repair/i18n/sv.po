# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <mika<PERSON>.a<PERSON>@mariaakerberg.com>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# Lasse L, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "%(name)s Sequence repair"
msgstr "%(name)s Reparation av sekvens"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "%(product)s: Insufficient Quantity To Repair"
msgstr "%(product)s: Otillräcklig mängd för att reparera"

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid "('Repair Order - %s' % (object.name))"
msgstr "(\"Reparationsorder - %s\" % (object.name))"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Recycle</i>)"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>Avlägsna</i>)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'New' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'Repaired' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""
"* Statusen \"Ny\" används när en användare kodar en ny och obekräftad reparationsorder.\n"
"* Statusen \"Bekräftad\" används när en användare bekräftar reparationsordern.\n"
"* Statusen \"Under reparation\" används när reparationen pågår.\n"
"* Status \"Reparerad\" sätts när reparationen är slutförd.\n"
"* Statusen \"Avbruten\" används när användaren avbryter reparationsordern."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(Lägg till)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Confirmed</span>"
msgstr "<span class=\"col-6\">Konfirmerad</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">Sent</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Under Repair</span>"
msgstr "<span class=\"col-6\">Under reparation</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Done:</span>"
msgstr "<span class=\"o_stat_text\">Utförd:</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repair Parts:</span>"
msgstr "<span class=\"o_stat_text\">Reparationsdelar:</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr "<span class=\"o_stat_text\">Säljorder</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">To Do:</span>"
msgstr "<span class=\"o_stat_text\">Att göra:</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>Order</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<span>Repair Order #</span>"
msgstr "<span>Reparationsorder #</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Customer:</strong>"
msgstr "<strong>Kund:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial:</strong>"
msgstr "<strong>Parti/Serie:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product:</strong>"
msgstr "<strong>Produkt:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Responsible:</strong>"
msgstr "<strong>Ansvarig:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Status:</strong>"
msgstr "<strong>Status:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Detta kan leda till inkonsekvenser i din inventering."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Åtgärd Krävs"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undantaget dekoration"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Aktivitetstillstånd"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon för aktivitetstyp"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_order_view_activity
msgid "Activity view"
msgstr "Aktivitetsvy"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__add
msgid "Add"
msgstr "Lägg till"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add a line"
msgstr "Lägg till en rad"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr "Lägg till interna anteckningar."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_dest_id
msgid "Added Parts Destination Location"
msgstr "Tillagda delar Destination Plats"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__after
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "After"
msgstr "Efter"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "All"
msgstr "Alla"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_available
msgid "All Parts are available"
msgstr "Alla delar finns tillgängliga"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__allowed_lot_ids
msgid "Allowed Lot"
msgstr "Tillåtet parti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "Tillåtet att reservera produktion"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "Tillåtet att inte reservera produktion"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_late
msgid "Any Part is late"
msgstr "Alla delar är sena"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "Antal Bilagor"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__available
msgid "Available"
msgstr "Tillgänglig"

#. module: repair
#. odoo-javascript
#: code:addons/repair/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Repair"
msgstr "Tillbaka till reparation"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__before
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Before"
msgstr "Före"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "BoM Components"
msgstr "BoM-komponenter"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr "Jag kan inte hitta någon produktionsplats."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "Avbryt reparationen"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Cancelled"
msgstr "Annullerad"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Catalog"
msgstr "Katalog"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "Kategori"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Check availability"
msgstr "Kontrollera tillgänglighet"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""
"Välj den partner som ordern ska faktureras och levereras till. Du kan hitta "
"en partner genom dess namn, TIN, e-post eller interna referens."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "Färgindex"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Bolag"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Source Location"
msgstr "Komponent Källa Plats"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability
msgid "Component Status"
msgstr "Komponent Status"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Configuration"
msgstr "Konfiguration"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Bekräfta reparation"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "Bekräftad"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Omräkning av enheter kan endast ske om de tillhör samma kategori. "
"Omräkningen baseras på ett givet förhållande."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Quotation"
msgstr "Skapa offert"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields,field_description:repair.field_product_product__create_repair
#: model:ir.model.fields,field_description:repair.field_product_template__create_repair
msgid "Create Repair"
msgstr "Skapa reparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__is_repairable
msgid "Create Repair Orders from Returns"
msgstr "Skapa reparationsorder från returer"

#. module: repair
#: model:ir.model.fields,help:repair.field_product_product__create_repair
#: model:ir.model.fields,help:repair.field_product_template__create_repair
msgid ""
"Create a linked Repair Order on Sale Order confirmation of this product."
msgstr ""
"Skapa en länkad reparationsorder på bekräftelse av försäljningsorder för "
"denna produkt."

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "Skapa en ny etikett"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "Kund"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__search_date_category
msgid "Date Category"
msgstr "Datum Kategori"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Demand"
msgstr "Efterfrågan"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Description"
msgstr "Beskrivning"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Details"
msgstr "Detaljer"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr "Bekräftar du att du vill reparera"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Done"
msgstr "Klar"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "Avsluta reparation"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Säkerställ spårbarheten för en lagringsförd produkt i ditt lager."

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Exp %s"
msgstr "Exp %s"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__expected
msgid "Expected"
msgstr "Förväntat"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Följare"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Följare (kontakter)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome-ikon t.ex. fa-tasks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"For some of the parts, there is a difference between the initial demand and "
"the actual quantity that was used. Are you sure you want to confirm ?"
msgstr ""
"För vissa av delarna finns det en skillnad mellan den ursprungliga "
"efterfrågan och den faktiska kvantitet som användes. Är du säker på att du "
"vill bekräfta?"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecast Report"
msgstr "Prognosrapport"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecasted"
msgstr "Prognostiserad"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Framtida aktiviteter"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Gruppera efter"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "Har meddelande"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_uncomplete_moves
msgid "Has Uncomplete Moves"
msgstr "Har ofullständiga rörelser"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon för att indikera en undantagsaktivitet."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Om markerad så finns det meddelanden som kräver din uppmärksamhet."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Om markerad, en del meddelanden har leveransfel."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__under_warranty
msgid ""
"If ticked, the sales price will be set to 0 for all products transferred "
"from the repair order."
msgstr ""
"Om krysset är markerat kommer försäljningspriset att sättas till 0 för alla "
"produkter som överförs från reparationsordern."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__is_repairable
msgid ""
"If ticked, you will be able to directly create repair orders from a return."
msgstr ""
"Om kryssrutan är markerad kommer du att kunna skapa reparationsorder direkt "
"från en retur."

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"I en reparationsorder kan du beskriva de komponenter som du tar bort\n"
"                eller byter ut och registrera den tid du lagt ner på de olika\n"
"                åtgärder."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__in_repair_count
msgid "In repair count"
msgstr "I reparationsantal"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "In the Repair Order"
msgstr "I reparationsordern"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Interna anteckningar"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Inventory Move"
msgstr "Lagerrörelse"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr "Lager flyttas"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Är Följare"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__is_repairable
msgid "Is Repairable"
msgstr "Är reparerbar"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Jane Smith"
msgstr "Jane Smith"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "John Doe"
msgstr "John Doe"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "L12345"
msgstr "L12345"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Laptop"
msgstr "Bärbar dator"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__late
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late"
msgstr "Försenad(e)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Sena aktiviteter"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_availability
msgid ""
"Latest parts availability status for this RO. If green, then the RO's "
"readiness status is ready."
msgstr ""
"Senaste status för reservdelarnas tillgänglighet för denna RO. Om den är "
"grön är RO:ns beredskapsstatus klar."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Plats"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Locations"
msgstr "Lagerplatser"

#. module: repair
#: model:ir.model,name:repair.model_stock_lot
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Parti/Serie"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "Fel vid leverans meddelande"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Meddelanden"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Miscellaneous"
msgstr "Diverse"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mina aktiviteters sluttid"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "New"
msgstr "Ny"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nästa Kalenderhändelse för Aktivitet"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nästa slutdatum för aktivitet"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Nästa aktivitetssammanställning"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Nästa aktivitetstyp"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr "Ingen reparationsorder hittades. Låt oss skapa en!"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "Normal"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Not Available"
msgstr "Inte tillgänglig"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"Note that the warehouses of the return and repair locations don't match!"
msgstr ""
"Observera att lagren för retur- och reparationsplatserna inte stämmer "
"överens!"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal åtgärder"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_late
msgid "Number of Late Repair Orders"
msgstr "Antal sena reparationsorder"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_confirmed
msgid "Number of Repair Orders Confirmed"
msgstr "Antal bekräftade reparationsorder"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_under_repair
msgid "Number of Repair Orders Under Repair"
msgstr "Antal reparationsorder under reparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_ready
msgid "Number of Repair Orders to Process"
msgstr "Antal reparationsorder som ska behandlas"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fel"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelanden som kräver åtgärd"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal meddelanden med leveransfel"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__nbr_repairs
msgid "Number of repairs linked to this picking"
msgstr "Antal reparationer kopplade till denna plockning"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Open"
msgstr "Öppna"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_type_id
msgid "Operation Type"
msgstr "Operationstyp"

#. module: repair
#. odoo-python
#: code:addons/repair/models/product.py:0
#: code:addons/repair/models/repair.py:0
msgid "Operation not supported"
msgstr "Funktionen stöds inte"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Operationer"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_order_menu
msgid "Orders"
msgstr "Order"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_picking_type_menu
msgid "Overview"
msgstr "Översikt"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_ids
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr "Delar"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability_state
msgid "Parts Availability State"
msgstr "Tillgänglighet för reservdelar"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Pending"
msgstr "Avvaktande"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_ids
msgid "Picking Product"
msgstr "Plockning av produkt"

#. module: repair
#: model:ir.model,name:repair.model_stock_picking_type
msgid "Picking Type"
msgstr "Plocktyp"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "Prioritet"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__procurement_group_id
msgid "Procurement Group"
msgstr "Anskaffningsgrupp"

#. module: repair
#: model:ir.model,name:repair.model_product_template
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Produkt"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product A"
msgstr "Produkt A"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product B"
msgstr "Produkt B"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_product_product__product_catalog_product_is_in_repair
msgid "Product Catalog Product Is In Repair"
msgstr "Produktkatalog Produkten är i reparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_dest_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_dest_id
msgid "Product Destination Location"
msgstr "Produkt Destination Plats"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "Produktflyttar"

#. module: repair
#: model:ir.model,name:repair.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produktflyttar (lagerflyttsrad)"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Produktantal"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_src_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_src_id
msgid "Product Source Location"
msgstr "Produktkälla Plats"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "Produktspårning"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Produktens måttenhet"

#. module: repair
#: model:ir.model,name:repair.model_product_product
msgid "Product Variant"
msgstr "Produktvariant"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_product
msgid "Product Variants"
msgstr "Produktvarianter"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Produkt att reparera"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_template
msgid "Products"
msgstr "Produkter"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "Alla reparerade produkter hör till detta parti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_properties
msgid "Properties"
msgstr "Fastigheter"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "Quant"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "Antal"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "RO123456"
msgstr "RO123456"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__rating_ids
msgid "Ratings"
msgstr "Betyg"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready"
msgstr "Redo"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__recycle
msgid "Recycle"
msgstr "Återvinna"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid "Recycle Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__recycle_location_id
msgid "Recycled Parts Destination Location"
msgstr "Återvunna delar Destination Plats"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__remove
msgid "Remove"
msgstr "Ta bort"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_remove_location_dest_id
msgid "Remove Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_location_id
msgid "Removed Parts Destination Location"
msgstr "Borttagna delar Destinationsplats"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_picking__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
#: model:ir.model.fields.selection,name:repair.selection__stock_picking_type__code__repair_operation
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
msgid "Repair"
msgstr "Reparera"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_mto_pull_id
msgid "Repair MTO Rule"
msgstr "Reparation MTO-regel"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr "Reparationsanteckningar"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_type_id
msgid "Repair Operation Type"
msgstr "Typ av reparationsåtgärd"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
#: model:ir.model,name:repair.model_repair_order
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "Reparationsorder"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_count
msgid "Repair Order(s)"
msgstr "Reparationsorder(n)"

#. module: repair
#. odoo-python
#: code:addons/repair/models/sale_order.py:0
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.actions.act_window,name:repair.action_picking_repair
#: model:ir.actions.act_window,name:repair.action_picking_repair_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_form
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_line_ids
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "Reparationsorder"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr "Reparationsorder Taggar"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_picking_type_kanban
msgid "Repair Overview"
msgstr "Översikt över reparationer"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__repair_properties_definition
msgid "Repair Properties"
msgstr "Reparationsegenskaper"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "Referens för reparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_request
msgid "Repair Request"
msgstr "Begäran om reparation"

#. module: repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr "Reparationstjänster"

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr "Reparera taggar"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Repair must be under repair in order to end reparation."
msgstr ""
"Reparationen måste vara under reparation för att reparationen ska kunna "
"avslutas."

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_lot.py:0
msgid "Repair orders of %s"
msgstr "Reparationsorder på %s"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_part_count
msgid "Repair part count"
msgstr "Antal reparationsdelar"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repaired"
msgstr "Reparerad"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repaired_count
msgid "Repaired count"
msgstr "Reparerad räkning"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
#: model:stock.picking.type,name:repair.picking_type_warehouse0_repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_sale_order_form_inherit_repair
msgid "Repairs"
msgstr "Reparationer"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Reparationsorder"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr "Påfyllning på order (MTO)"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Reporting"
msgstr "Rapportering"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "Ansvarig"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Ansvarig användare"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_id
msgid "Return"
msgstr "Returnera"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__picking_id
msgid "Return Order from which the product to be repaired comes from."
msgstr "Returorder som den produkt som ska repareras kommer ifrån."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__return_type_of_ids
msgid "Return Type Of"
msgstr "Retur Typ av"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_returned
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Returned"
msgstr "Returnerad"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveransfel"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Sale Order"
msgstr "Order"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line"
msgstr "Kundorderrad"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__repair_request
msgid "Sale Order Line Description."
msgstr "Försäljningsorder Linjebeskrivning."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line from which the Repair Order comes from."
msgstr "Sale Order Line som reparationsordern kommer från."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the Repair Order comes from."
msgstr "Försäljningsorder från vilken reparationsordern kommer."

#. module: repair
#: model:ir.model,name:repair.model_sale_order
msgid "Sales Order"
msgstr "Order"

#. module: repair
#: model:ir.model,name:repair.model_sale_order_line
msgid "Sales Order Line"
msgstr "Orderrad"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "Schemalagt datum"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "Sök reparationsorder"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Serial number is required for product to repair : %s"
msgstr "Serienummer krävs för att produkten ska kunna repareras: %s"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Sätt till utkast"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr "Visa alla poster som har nästa händelse före idag"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "Starta reparationen"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Status"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baserad på aktiviteter\n"
"Försenade: Leveranstidpunkten har passerat\n"
"Idag: Aktivitetsdatum är idag\n"
"Kommande: Framtida aktiviteter."

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Lagerflytt"

#. module: repair
#: model:ir.model,name:repair.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Lagerpåfyllnadsrapport"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "Etikettnamn"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr "Tagnamnet finns redan!"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "Etiketter"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr "Tekniskt fält för att kontrollera när vi kan reservera kvantiteter"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "Tekniskt fält för att kontrollera när vi kan avreservera"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "The day after tomorrow"
msgstr "I övermorgon"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""
"Den måttenhet för produkten som du har valt har en annan kategori än "
"måttenheten för produkten."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "This is a repair note."
msgstr "Detta är en reparationsanteckning."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_dest_id
msgid ""
"This is the default destination location for the product to be repaired in "
"repair orders with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid ""
"This is the default recycle destination location when you create a repair "
"order with this operation type."
msgstr ""
"Detta är standardplatsen för återvinning när du skapar en reparationsorder "
"med denna åtgärdstyp."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_remove_location_dest_id
msgid ""
"This is the default remove destination location when you create a repair "
"order with this operation type."
msgstr ""
"Detta är standardplatsen för borttagning när du skapar en reparationsorder "
"med den här åtgärdstypen."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_src_id
msgid ""
"This is the default source location for the product to be repaired in repair"
" orders with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid ""
"This is the location where the components of product to repair is located."
msgstr ""
"Detta är den plats där komponenterna i den produkt som ska repareras finns."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_location_src_id
msgid "This is the location where the product to repair is located."
msgstr "Detta är den plats där den produkt som ska repareras finns."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_location_id
#: model:ir.model.fields,help:repair.field_repair_order__recycle_location_id
msgid "This is the location where the repair parts are located."
msgstr "Detta är den plats där reparationsdelarna finns."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_dest_id
#: model:ir.model.fields,help:repair.field_repair_order__product_location_dest_id
msgid "This is the location where the repaired product is located."
msgstr "Detta är den plats där den reparerade produkten finns."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "To Repair"
msgstr "För reparation"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__today
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today"
msgstr "Idag"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Tomorrow"
msgstr "I morgon"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Spårbarhetsrapport"

#. module: repair
#: model:ir.model,name:repair.model_stock_picking
msgid "Transfer"
msgstr "Flytt"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__is_returned
msgid ""
"True if this repair is linked to a Return Order and the order is 'Done'. "
"False otherwise."
msgstr ""
"Sant om denna reparation är kopplad till en returorder och ordern är "
"\"Klar\". Falskt i annat fall."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_line_type
msgid "Type"
msgstr "Typ"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Typ av operation"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ av undantagsaktivitet som har registrerats."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Under Repair"
msgstr "Under reparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__under_warranty
msgid "Under Warranty"
msgstr "Under garanti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "Måttenhet"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Units"
msgstr "Enheter"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Unreserve"
msgstr "Avboka"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "Brådskande"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Used"
msgstr "Använda"

#. module: repair
#: model:ir.model,name:repair.model_stock_warehouse
msgid "Warehouse"
msgstr "Lager"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr "Varna för otillräcklig reparationsmängd"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Warning"
msgstr "Varning"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Webbplatsmeddelanden"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "Webbplatsens kommunikationshistorik"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Yesterday"
msgstr "Igår"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You can not enter negative quantities."
msgstr "Du kan inte ange negativa kvantiteter."

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You cannot cancel a Repair Order that's already been completed"
msgstr "Du kan inte avbeställa en reparationsorder som redan har slutförts"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You cannot create a quotation for a repair order that is already linked to an existing sale order.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""
"Du kan inte skapa en offert för en reparationsorder som redan är kopplad till en befintlig försäljningsorder.\n"
"Berörd(a) reparationsorder(s):\n"
"%(ref_str)s"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You need to define a customer for a repair order in order to create an associated quotation.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""
"Du måste definiera en kund för en reparationsorder för att kunna skapa en tillhörande offert.\n"
"Berörd(a) reparationsorder(s):\n"
"%(ref_str)s"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr "från plats"

#. module: repair
#: model:ir.actions.server,name:repair.action_repair_overview
msgid "stock.repair.type.overview"
msgstr "lager.reparation.typ.översikt"
