# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# Szabolcs Máj, 2024
# f1b3a33e3b33fcf18004a5292e501f50_3500ca8 <373b677b151624c4521d9efc77b996fd_750224>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> Tibor <<EMAIL>>, 2024
# <PERSON><PERSON>gy <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# krnkris, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "%(name)s Sequence repair"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "%(product)s: Insufficient Quantity To Repair"
msgstr ""

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid "('Repair Order - %s' % (object.name))"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Recycle</i>)"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>Eltávolít</i>)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'New' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'Repaired' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(Hozzáad)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Confirmed</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Under Repair</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Done:</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repair Parts:</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr "<span class=\"o_stat_text\">Vevői rendelés</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">To Do:</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>Rendelések</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<span>Repair Order #</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Customer:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Responsible:</strong>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Status:</strong>"
msgstr "<strong>Állapot:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Akció szükséges"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Tevékenységek"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tevékenység kivétel dekoráció"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Tevékenység állapot"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tevékenység típus ikon"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_order_view_activity
msgid "Activity view"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__add
msgid "Add"
msgstr "Hozzáadás"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add a line"
msgstr "Új sor"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_dest_id
msgid "Added Parts Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__after
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "After"
msgstr "Utána"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "All"
msgstr "Összes"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_available
msgid "All Parts are available"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__allowed_lot_ids
msgid "Allowed Lot"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__reserve_visible
msgid "Allowed to Reserve Production"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_late
msgid "Any Part is late"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "Mellékletek száma"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__available
msgid "Available"
msgstr "Elérhető"

#. module: repair
#. odoo-javascript
#: code:addons/repair/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__before
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Before"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "BoM Components"
msgstr "Komponensek anyagjegyzéke"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr "Nem található termelési hely."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "Javítás visszavonása"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Cancelled"
msgstr "Visszavont"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Catalog"
msgstr "Katalógus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "Kategória"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Check availability"
msgstr "Elérhetőség ellenőrzése"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "Szín index"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Vállalat"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Source Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability
msgid "Component Status"
msgstr ""

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Configuration"
msgstr "Konfiguráció"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Javítás megerősítése"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "Megerősített"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"A mértékegységek közötti átváltás csak akkor lehetséges, ha ugyanabba a "
"kategóriába tartoznak. Az átváltás az arányszámok alapján történik."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Quotation"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields,field_description:repair.field_product_product__create_repair
#: model:ir.model.fields,field_description:repair.field_product_template__create_repair
msgid "Create Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__is_repairable
msgid "Create Repair Orders from Returns"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_product_product__create_repair
#: model:ir.model.fields,help:repair.field_product_template__create_repair
msgid ""
"Create a linked Repair Order on Sale Order confirmation of this product."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "Új címke létrehozása"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "Ügyfél"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__search_date_category
msgid "Date Category"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Demand"
msgstr "Igény"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Description"
msgstr "Leírás"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Details"
msgstr "Részletek"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Done"
msgstr "Kész"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "Javítás vége"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Raktárban tárolható termékek nyomonkövetésének biztosítása."

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Exp %s"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__expected
msgid "Expected"
msgstr "Várható"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Követők"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Követők (Partnerek)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikon pld: fa-tasks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"For some of the parts, there is a difference between the initial demand and "
"the actual quantity that was used. Are you sure you want to confirm ?"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecast Report"
msgstr "Előrejelzés kimutatás"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecasted"
msgstr "Előrejelzett"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Jövőbeni tevékenységek"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Csoportosítás"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "Van üzenet"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_uncomplete_moves
msgid "Has Uncomplete Moves"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kivétel tevékenységet jelző ikon"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ha be van jelölve, akkor az új üzenetek figyelmet igényelnek."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Ha be van jelölve, akkor néhány üzenetnél kézbesítési hiba lépett fel."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__under_warranty
msgid ""
"If ticked, the sales price will be set to 0 for all products transferred "
"from the repair order."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__is_repairable
msgid ""
"If ticked, you will be able to directly create repair orders from a return."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"A javítási megrendelésen, részletezheti a kivett, alkatrészeket, hozzáadott,\n"
"                kicserélt alkatrészeket és feljegyezheti a különböző műveletekre \n"
"                eltöltött időt."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__in_repair_count
msgid "In repair count"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "In the Repair Order"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Belső jegyzetek"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Inventory Move"
msgstr "Raktárkészlet mozgás"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr "Leltár mozgások"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Követő"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__is_repairable
msgid "Is Repairable"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Jane Smith"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "John Doe"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "L12345"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Laptop"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__late
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late"
msgstr "Késő"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Késő tevékenységek"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_availability
msgid ""
"Latest parts availability status for this RO. If green, then the RO's "
"readiness status is ready."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Lokáció"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Locations"
msgstr "Tárhelyek"

#. module: repair
#: model:ir.model,name:repair.model_stock_lot
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Lot/szériaszám"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "Üzenetkézbesítési hiba"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Üzenetek"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Miscellaneous"
msgstr "Egyéb"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Tevékenységeim határideje"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "New"
msgstr "Új"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Következő tevékenység naptár esemény"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Következő tevékenység határideje"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Következő tevékenység összegzése"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Következő tevékenység típusa"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "Normál"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Not Available"
msgstr "Nem elérhető"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"Note that the warehouses of the return and repair locations don't match!"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Akciók száma"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_late
msgid "Number of Late Repair Orders"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_confirmed
msgid "Number of Repair Orders Confirmed"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_under_repair
msgid "Number of Repair Orders Under Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_ready
msgid "Number of Repair Orders to Process"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "Hibák száma"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Üzenetek száma, melyek akciót igényelnek"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kézbesítési hibával rendelkező üzenetek száma"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__nbr_repairs
msgid "Number of repairs linked to this picking"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Open"
msgstr "Megnyitás"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_type_id
msgid "Operation Type"
msgstr "Művelet típus"

#. module: repair
#. odoo-python
#: code:addons/repair/models/product.py:0
#: code:addons/repair/models/repair.py:0
msgid "Operation not supported"
msgstr "A művelet nem támogatott"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Műveletek"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_order_menu
msgid "Orders"
msgstr "Rendelések"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_picking_type_menu
msgid "Overview"
msgstr "Áttekintés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_ids
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr "Részek"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability_state
msgid "Parts Availability State"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Pending"
msgstr "Függő"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_ids
msgid "Picking Product"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_stock_picking_type
msgid "Picking Type"
msgstr "Kigyűjtés típusa"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "Prioritás"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__procurement_group_id
msgid "Procurement Group"
msgstr "Beszerzési csoport"

#. module: repair
#: model:ir.model,name:repair.model_product_template
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Termék"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product A"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product B"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_product_product__product_catalog_product_is_in_repair
msgid "Product Catalog Product Is In Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_dest_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_dest_id
msgid "Product Destination Location"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "Termékmozgások"

#. module: repair
#: model:ir.model,name:repair.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Termékmozgások (Készletmozgás sor)"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Termékmennyiség"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_src_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_src_id
msgid "Product Source Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "Termék követés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Termék mértékegység"

#. module: repair
#: model:ir.model,name:repair.model_product_product
msgid "Product Variant"
msgstr "Termékváltozat"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_product
msgid "Product Variants"
msgstr "Termékváltozatok"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Javítandő termék"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_template
msgid "Products"
msgstr "Termékek"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "Megjavított termékek ehhez a résztételhez/csomaghoz tartoznak"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_properties
msgid "Properties"
msgstr "Tulajdonságok"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "Kvant"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "Mennyiség"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "RO123456"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__rating_ids
msgid "Ratings"
msgstr "Értékelések"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready"
msgstr "Előkészítve"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__recycle
msgid "Recycle"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid "Recycle Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__recycle_location_id
msgid "Recycled Parts Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__remove
msgid "Remove"
msgstr "Eltávolítás"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_remove_location_dest_id
msgid "Remove Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_location_id
msgid "Removed Parts Destination Location"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_picking__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
#: model:ir.model.fields.selection,name:repair.selection__stock_picking_type__code__repair_operation
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
msgid "Repair"
msgstr "Javítás"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_mto_pull_id
msgid "Repair MTO Rule"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_type_id
msgid "Repair Operation Type"
msgstr ""

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
#: model:ir.model,name:repair.model_repair_order
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "Javítási rendelés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_count
msgid "Repair Order(s)"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/sale_order.py:0
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.actions.act_window,name:repair.action_picking_repair
#: model:ir.actions.act_window,name:repair.action_picking_repair_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_form
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_line_ids
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "Javítási rendelések"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr "Javítási rendelés címkék"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_picking_type_kanban
msgid "Repair Overview"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__repair_properties_definition
msgid "Repair Properties"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "Hivatkozás javításra"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_request
msgid "Repair Request"
msgstr ""

#. module: repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr "Javítás címkék"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Repair must be under repair in order to end reparation."
msgstr "Javításoknak javítás alatt kell lennie a javítás befejezéséhez."

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_lot.py:0
msgid "Repair orders of %s"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_part_count
msgid "Repair part count"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repaired"
msgstr "Javítvott"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repaired_count
msgid "Repaired count"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
#: model:stock.picking.type,name:repair.picking_type_warehouse0_repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_sale_order_form_inherit_repair
msgid "Repairs"
msgstr "Javítások"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Javítási rendelések"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr ""

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Reporting"
msgstr "Kimutatás"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "Felelős"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Felelős felhasználó"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_id
msgid "Return"
msgstr "Visszaküldés"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__picking_id
msgid "Return Order from which the product to be repaired comes from."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__return_type_of_ids
msgid "Return Type Of"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_returned
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Returned"
msgstr "Visszaküldött"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS kézbesítési hiba"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Sale Order"
msgstr "Vevői rendelés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line"
msgstr "Vevői rendelés sor"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__repair_request
msgid "Sale Order Line Description."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line from which the Repair Order comes from."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the Repair Order comes from."
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_sale_order
msgid "Sales Order"
msgstr "Vevői rendelés"

#. module: repair
#: model:ir.model,name:repair.model_sale_order_line
msgid "Sales Order Line"
msgstr "Vevői rendelés sor"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "Ütemezett dátum"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "Javítási rendelések keresése"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Serial number is required for product to repair : %s"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Piszkozatra állít"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Az összes olyan rekord megjelenítése, melynél a következő akció dátuma a mai"
" nap előtti"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "Javítás kezdete"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Státusz"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tevékenységeken alapuló állapot\n"
"Lejárt: A tevékenység határideje lejárt\n"
"Ma: A határidő ma van\n"
"Tervezett: Jövőbeli határidő."

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Készletmozgás"

#. module: repair
#: model:ir.model,name:repair.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Készletfeltöltés kimutatás"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "Címke neve"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr "A címke név már létezik!"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "Címkék"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "The day after tomorrow"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "This is a repair note."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_dest_id
msgid ""
"This is the default destination location for the product to be repaired in "
"repair orders with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid ""
"This is the default recycle destination location when you create a repair "
"order with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_remove_location_dest_id
msgid ""
"This is the default remove destination location when you create a repair "
"order with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_src_id
msgid ""
"This is the default source location for the product to be repaired in repair"
" orders with this operation type."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid ""
"This is the location where the components of product to repair is located."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_location_src_id
msgid "This is the location where the product to repair is located."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_location_id
#: model:ir.model.fields,help:repair.field_repair_order__recycle_location_id
msgid "This is the location where the repair parts are located."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_dest_id
#: model:ir.model.fields,help:repair.field_repair_order__product_location_dest_id
msgid "This is the location where the repaired product is located."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "To Repair"
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__today
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today"
msgstr "Ma"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Mai tevékenységek"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Tomorrow"
msgstr "Holnap"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Nyomonkövetés riport"

#. module: repair
#: model:ir.model,name:repair.model_stock_picking
msgid "Transfer"
msgstr "Átmozgatás"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__is_returned
msgid ""
"True if this repair is linked to a Return Order and the order is 'Done'. "
"False otherwise."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_line_type
msgid "Type"
msgstr "Típus"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Művelet típusa"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kivétel tevékenység típusa a rekordon."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Under Repair"
msgstr "Javítás alatt"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__under_warranty
msgid "Under Warranty"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "Mértékegység"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Units"
msgstr "Egység"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Unreserve"
msgstr "Foglalás feloldása"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "Sürgős"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Used"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_stock_warehouse
msgid "Warehouse"
msgstr "Raktár"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Warning"
msgstr "Figyelmeztetés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Weboldal üzenetek"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "Weboldal kommunikációs előzmények"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Yesterday"
msgstr "Tegnap"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You can not enter negative quantities."
msgstr "Nem lehet negatív mennyiséget megadni."

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You cannot cancel a Repair Order that's already been completed"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You cannot create a quotation for a repair order that is already linked to an existing sale order.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You need to define a customer for a repair order in order to create an associated quotation.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr ""

#. module: repair
#: model:ir.actions.server,name:repair.action_repair_overview
msgid "stock.repair.type.overview"
msgstr ""
