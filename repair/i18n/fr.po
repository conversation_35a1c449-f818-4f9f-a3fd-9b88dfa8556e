# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "%(name)s Sequence repair"
msgstr "%(name)s Séquence de réparation"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "%(product)s: Insufficient Quantity To Repair"
msgstr "%(product)s : Quantité insuffisante pour effectuer la réparation"

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid "('Repair Order - %s' % (object.name))"
msgstr "('Ordre de réparation - %s' % (object.name))"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Recycle</i>)"
msgstr "(<i>Recycler</i>)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>Supprimer</i>)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'New' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'Repaired' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""
"* Le statut 'Nouveau' est utilisé lorsqu'un utilisateur encode un nouvel ordre de réparation non confirmé.\n"
"* Le statut 'Confirmé' est utilisé lorsqu'un utilisateur confirme l'ordre de réparation.\n"
"* Le statut 'En réparation' est utilisé lorsque la réparation est en cours.\n"
"* Le statut 'Réparé' est utilisé lorsque la réparation est achevée.\n"
"* Le statut 'Annulé' est utilisé lorsque l'utilisateur annule l'ordre de réparation."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(Ajouter)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Confirmed</span>"
msgstr "<span class=\"col-6\">Confirmé</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">En retard</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span class=\"col-6\">Under Repair</span>"
msgstr "<span class=\"col-6\">En cours de réparation</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Done:</span>"
msgstr "<span class=\"o_stat_text\">Terminé :</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repair Parts:</span>"
msgstr "<span class=\"o_stat_text\">Pièces de rechange :</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr "<span class=\"o_stat_text\">Bon de commande</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">To Do:</span>"
msgstr "<span class=\"o_stat_text\">À faire :</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>Commandes</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<span>Repair Order #</span>"
msgstr "<span>Ordre de réparation #</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Customer:</strong>"
msgstr "<strong>Client :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial:</strong>"
msgstr "<strong>Lot/Série :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product:</strong>"
msgstr "<strong>Produit :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Responsible:</strong>"
msgstr "<strong>Responsable :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Status:</strong>"
msgstr "<strong>Statut :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Cela peut donner lieu à des incohérences dans votre inventaire."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Statut de l'activité"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_order_view_activity
msgid "Activity view"
msgstr "Vue d'activité"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__add
msgid "Add"
msgstr "Ajouter"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add a line"
msgstr "Ajouter une ligne"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr "Ajouter des notes internes."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_dest_id
msgid "Added Parts Destination Location"
msgstr "Emplacement de destination des pièces ajoutées"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__after
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "After"
msgstr "Après"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "All"
msgstr "Tous"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_available
msgid "All Parts are available"
msgstr "Toutes les pièces sont disponibles"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__allowed_lot_ids
msgid "Allowed Lot"
msgstr "Lot autorisé"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "Autorisé à réserver la production "

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "Autorisé à annuler la réservation de production"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_parts_late
msgid "Any Part is late"
msgstr "Toutes les pièces sont en retard"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__available
msgid "Available"
msgstr "Disponible"

#. module: repair
#. odoo-javascript
#: code:addons/repair/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Repair"
msgstr "Retour à la réparation"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__before
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Before"
msgstr "Avant"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "BoM Components"
msgstr "Composants de nomenclature"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr "Impossible de trouver un emplacement de production."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "Annuler la réparation"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Cancelled"
msgstr "Annulé"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Catalog"
msgstr "Catalogue"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "Catégorie"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Check availability"
msgstr "Vérifier la disponibilité"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""
"Choisissez le partenaire pour qui la commande sera facturée et livrée. Vous "
"pouvez trouver un partenaire par son nom, son NIF, son e-mail ou sa "
"référence interne."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "Couleur"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Société"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Destination Location"
msgstr "Emplacement de destination des composants"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
msgid "Component Source Location"
msgstr "Emplacement d'origine des composants"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability
msgid "Component Status"
msgstr "Statut des composants"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Configuration"
msgstr "Configuration"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Confirmer la réparation"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "Confirmé"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Il est possible de convertir deux unités de mesures si elles appartiennent à"
" la même catégorie. Cette conversion utilise les facteurs définis pour ces "
"unités."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Quotation"
msgstr "Créer le devis"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.model.fields,field_description:repair.field_product_product__create_repair
#: model:ir.model.fields,field_description:repair.field_product_template__create_repair
msgid "Create Repair"
msgstr "Créer une réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__is_repairable
msgid "Create Repair Orders from Returns"
msgstr "Créer des ordres de réparation à partir des retours"

#. module: repair
#: model:ir.model.fields,help:repair.field_product_product__create_repair
#: model:ir.model.fields,help:repair.field_product_template__create_repair
msgid ""
"Create a linked Repair Order on Sale Order confirmation of this product."
msgstr ""
"Créer un ordre de réparation associé lors de la confirmation d'un bon de "
"commande de ce produit."

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "Créer une nouvelle étiquette"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Créé le"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "Client"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__search_date_category
msgid "Date Category"
msgstr "Catégorie Date"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Demand"
msgstr "Demande"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Description"
msgstr "Description"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Details"
msgstr "Détails"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr "Confirmez-vous que vous voulez réparer"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Done"
msgstr "Terminé"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "Terminer la réparation"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Assurez la traçabilité d'un produit stockable dans votre entrepôt."

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Exp %s"
msgstr "Prévu %s"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__expected
msgid "Expected"
msgstr "Prévu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"For some of the parts, there is a difference between the initial demand and "
"the actual quantity that was used. Are you sure you want to confirm ?"
msgstr ""
"Pour certaines pièces, il y a une différence entre la demande initiale et la"
" quantité actuelle utilisée. Êtes-vous sûr de vouloir confirmer ?"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecast Report"
msgstr "Rapport de prévision"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Forecasted"
msgstr "Prévu"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Activités futures"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Regrouper par"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "A un message"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_uncomplete_moves
msgid "Has Uncomplete Moves"
msgstr "Contient des mouvements incomplets"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__under_warranty
msgid ""
"If ticked, the sales price will be set to 0 for all products transferred "
"from the repair order."
msgstr ""
"Si cette option est cochée, le prix de vente sera défini sur 0 pour tous les"
" produits transférés de l'ordre de réparation."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__is_repairable
msgid ""
"If ticked, you will be able to directly create repair orders from a return."
msgstr ""
"Si coché, vous pourrez directement créer des ordres de réparation à partir "
"d'un retour."

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"Dans un ordre de réparation, vous pouvez détailler les composants à supprimer,\n"
"à ajouter ou à remplacer et vous pouvez enregistrer le temps que vous avez passé sur les différentes\n"
"opérations."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__in_repair_count
msgid "In repair count"
msgstr "Quantité en réparation"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.product_view_search_catalog
msgid "In the Repair Order"
msgstr "Dans l'ordre de réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Notes internes"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Inventory Move"
msgstr "Mouvement de stock"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr "Mouvements de stock"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__is_repairable
msgid "Is Repairable"
msgstr "Est réparable"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Jane Smith"
msgstr "Jane Smith"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "John Doe"
msgstr "John Doe"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "L12345"
msgstr "L12345"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Laptop"
msgstr "Ordinateur portable"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__parts_availability_state__late
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late"
msgstr "En retard"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Activités en retard"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_availability
msgid ""
"Latest parts availability status for this RO. If green, then the RO's "
"readiness status is ready."
msgstr ""
"Dernier statut de disponibilité des pièces pour cet OR. Si vert, le statut "
"de préparation de l'OR est prêt."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Emplacement"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Locations"
msgstr "Emplacements"

#. module: repair
#: model:ir.model,name:repair.model_stock_lot
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Lot/série"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Messages"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Miscellaneous"
msgstr "Divers"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "New"
msgstr "Nouveau"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l'activité suivante"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr "Aucun ordre de réparation trouvé. Créons-en un !"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "Normal"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Not Available"
msgstr "Pas disponible"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"Note that the warehouses of the return and repair locations don't match!"
msgstr ""
"Notez que les entrepôts des emplacements de retour et de réparation ne "
"correspondent pas !"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_late
msgid "Number of Late Repair Orders"
msgstr "Nombre d'ordres de réparation en retard"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_confirmed
msgid "Number of Repair Orders Confirmed"
msgstr "Nombre d'ordres de réparation confirmés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_under_repair
msgid "Number of Repair Orders Under Repair"
msgstr "Nombre d'ordres de réparation en cours"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__count_repair_ready
msgid "Number of Repair Orders to Process"
msgstr "Nombre d'ordres de réparation à traiter"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking__nbr_repairs
msgid "Number of repairs linked to this picking"
msgstr "Nombre de réparations liées à ce transfert"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Open"
msgstr "Ouvrir"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_type_id
msgid "Operation Type"
msgstr "Type d'opération"

#. module: repair
#. odoo-python
#: code:addons/repair/models/product.py:0
#: code:addons/repair/models/repair.py:0
msgid "Operation not supported"
msgstr "Opération non prise en charge"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Opérations"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_order_menu
msgid "Orders"
msgstr "Commandes"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_picking_type_menu
msgid "Overview"
msgstr "Vue d'ensemble"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_ids
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr "Pièces"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_availability_state
msgid "Parts Availability State"
msgstr "Statut de disponibilité des pièces"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Pending"
msgstr "En attente"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_ids
msgid "Picking Product"
msgstr "Produit de transfert"

#. module: repair
#: model:ir.model,name:repair.model_stock_picking_type
msgid "Picking Type"
msgstr "Type de transfert"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "Priorité"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__procurement_group_id
msgid "Procurement Group"
msgstr "Groupe d'approvisionnement"

#. module: repair
#: model:ir.model,name:repair.model_product_template
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Produit"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product A"
msgstr "Produit A"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Product B"
msgstr "Produit B"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_product_product__product_catalog_product_is_in_repair
msgid "Product Catalog Product Is In Repair"
msgstr "Produit du catalogue produit en cours de réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_dest_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_dest_id
msgid "Product Destination Location"
msgstr "Emplacement de destination du produit"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "Mouvements de produits"

#. module: repair
#: model:ir.model,name:repair.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mouvements de produit (Ligne de mouvement de stock)"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Quantité de produits"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_location_src_id
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_product_location_src_id
msgid "Product Source Location"
msgstr "Emplacement d'origine du produit"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "Suivi de produit"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Unité de mesure du produit"

#. module: repair
#: model:ir.model,name:repair.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_product
msgid "Product Variants"
msgstr "Variantes de produits"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Produit à réparer"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_product_template
msgid "Products"
msgstr "Produits"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "Les produits réparés appartiennent tous à ce lot"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_properties
msgid "Properties"
msgstr "Propriétés"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "Qté"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "Quantité"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "RO123456"
msgstr "RO123456"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready"
msgstr "Prêt"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__recycle
msgid "Recycle"
msgstr "Recycler"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid "Recycle Destination Location"
msgstr "Emplacement de destination recyclage"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__recycle_location_id
msgid "Recycled Parts Destination Location"
msgstr "Emplacement de destination des pièces recyclées"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__stock_move__repair_line_type__remove
msgid "Remove"
msgstr "Supprimer"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__default_remove_location_dest_id
msgid "Remove Destination Location"
msgstr "Supprimer l'emplacement de destination"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__parts_location_id
msgid "Removed Parts Destination Location"
msgstr "Emplacement de destination des pièces supprimées"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_picking__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
#: model:ir.model.fields.selection,name:repair.selection__stock_picking_type__code__repair_operation
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
msgid "Repair"
msgstr "Réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_mto_pull_id
msgid "Repair MTO Rule"
msgstr "Règle de réparation MTO"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr "Notes de la réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warehouse__repair_type_id
msgid "Repair Operation Type"
msgstr "Type d'opération de réparation"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
#: model:ir.model,name:repair.model_repair_order
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "Ordre de réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_sale_order__repair_count
msgid "Repair Order(s)"
msgstr "Ordre(s) de réparation"

#. module: repair
#. odoo-python
#: code:addons/repair/models/sale_order.py:0
#: code:addons/repair/models/stock_picking.py:0
#: model:ir.actions.act_window,name:repair.action_picking_repair
#: model:ir.actions.act_window,name:repair.action_picking_repair_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_form
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_line_ids
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_form
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "Ordres de réparation"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr "Étiquettes des ordres de réparation"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_picking_type_kanban
msgid "Repair Overview"
msgstr "Aperçu des réparations"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__repair_properties_definition
msgid "Repair Properties"
msgstr "Propriétés de réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "Référence de la réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repair_request
msgid "Repair Request"
msgstr "Demande de réparation"

#. module: repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr "Services de réparation"

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr "Etiquettes de réparation"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Repair must be under repair in order to end reparation."
msgstr "La réparation doit être en cours pour pouvoir prendre fin."

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_lot.py:0
msgid "Repair orders of %s"
msgstr "Ordres de réparation de %s"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repair_part_count
msgid "Repair part count"
msgstr "Nombre de pièces de rechange"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repaired"
msgstr "Réparé"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_lot__repaired_count
msgid "Repaired count"
msgstr "Nombre de réparations"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
#: model:stock.picking.type,name:repair.picking_type_warehouse0_repair
#: model_terms:ir.ui.view,arch_db:repair.repair_view_picking_type_form
#: model_terms:ir.ui.view,arch_db:repair.view_sale_order_form_inherit_repair
msgid "Repairs"
msgstr "Réparations"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Ordre de réparation"

#. module: repair
#. odoo-python
#: code:addons/repair/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr "Réapprovisionner sur commande (MTO)"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "Reporting"
msgstr "Analyse"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__picking_id
msgid "Return"
msgstr "Retour"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__picking_id
msgid "Return Order from which the product to be repaired comes from."
msgstr "Ordre de retour duquel provient le produit à réparer."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__return_type_of_ids
msgid "Return Type Of"
msgstr "Type de retour de"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__is_returned
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Returned"
msgstr "Retourné"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Sale Order"
msgstr "Commande client"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line"
msgstr "Ligne de commande"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__repair_request
msgid "Sale Order Line Description."
msgstr "Description de la ligne de commande"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_line_id
msgid "Sale Order Line from which the Repair Order comes from."
msgstr "Ligne de commande de laquelle provient l'ordre de réparation."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the Repair Order comes from."
msgstr "Bon de commande duquel provient l'ordre de réparation."

#. module: repair
#: model:ir.model,name:repair.model_sale_order
msgid "Sales Order"
msgstr "Commande client"

#. module: repair
#: model:ir.model,name:repair.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "Date planifiée"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "Rechercher un ordre de réparation"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Serial number is required for product to repair : %s"
msgstr "Le numéro de série est requis pour pouvoir réparer le produit : %s"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Marquer comme brouillon"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez tous les enregistrements pour lesquels la date des prochaines "
"actions est pour aujourd'hui ou avant. "

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "Commencer la réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Statut"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: repair
#: model:ir.model,name:repair.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Rapport de réassorts de stock"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "Nom de l'étiquette"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr "Ce nom d'étiquette existe déjà !"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "Étiquettes"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr ""
"Champ technique pour vérifier quand les quantités peuvent être réservées"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "Champ technique à cocher si nous pouvons annuler la réservation"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "The day after tomorrow"
msgstr "Après-demain"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""
"L'unité de mesure que vous avez choisie est dans une catégorie différente "
"que l'unité de mesure du produit."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "This is a repair note."
msgstr "Ceci est une note de réparation."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_dest_id
msgid ""
"This is the default destination location for the product to be repaired in "
"repair orders with this operation type."
msgstr ""
"Il s'agit de l'emplacement de destination par défaut du produit à réparer "
"dans les ordres de réparation avec ce type d'opération."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_recycle_location_dest_id
msgid ""
"This is the default recycle destination location when you create a repair "
"order with this operation type."
msgstr ""
"Il s'agit de la destination de recyclage par défaut lorsque vous créez un "
"ordre de réparation avec ce type d'opération."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_remove_location_dest_id
msgid ""
"This is the default remove destination location when you create a repair "
"order with this operation type."
msgstr ""
"Il s'agit de la destination de suppression par défaut lorsque vous créez un "
"ordre de réparation avec ce type d'opération."

#. module: repair
#: model:ir.model.fields,help:repair.field_stock_picking_type__default_product_location_src_id
msgid ""
"This is the default source location for the product to be repaired in repair"
" orders with this operation type."
msgstr ""
"Il s'agit de l'emplacement source par défaut du produit à réparer dans les "
"ordres de réparation avec ce type d'opération."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid ""
"This is the location where the components of product to repair is located."
msgstr ""
"Il s'agit de l'emplacement où se trouvent les composants du produit à "
"réparer."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__product_location_src_id
msgid "This is the location where the product to repair is located."
msgstr "Il s'agit de l'emplacement où le produit à réparer se situe."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__parts_location_id
#: model:ir.model.fields,help:repair.field_repair_order__recycle_location_id
msgid "This is the location where the repair parts are located."
msgstr "Il s'agit de l'emplacement où se situent les pièces de réparation."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_dest_id
#: model:ir.model.fields,help:repair.field_repair_order__product_location_dest_id
msgid "This is the location where the repaired product is located."
msgstr "Il s'agit de l'emplacement où se situe le produit réparé."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_repair_type_kanban
msgid "To Repair"
msgstr "À réparer"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__today
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today"
msgstr "Aujourd'hui"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Activités du jour"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Tomorrow"
msgstr "Demain"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Rapport de traçabilité"

#. module: repair
#: model:ir.model,name:repair.model_stock_picking
msgid "Transfer"
msgstr "Transfert"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__is_returned
msgid ""
"True if this repair is linked to a Return Order and the order is 'Done'. "
"False otherwise."
msgstr ""
"Vrai si cette réparation est liée à un retour et que la commande est "
"'Terminée'. Sinon faux."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_line_type
msgid "Type"
msgstr "Type"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Type d'opération"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Under Repair"
msgstr "En réparation"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__under_warranty
msgid "Under Warranty"
msgstr "Sous garantie"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Units"
msgstr "Unités"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Unreserve"
msgstr "Annuler la réservation"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "Urgent"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Used"
msgstr "Consommé"

#. module: repair
#: model:ir.model,name:repair.model_stock_warehouse
msgid "Warehouse"
msgstr "Entrepôt"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr "Avertissement d'une quantité de réparation insuffisante"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "Warning"
msgstr "Avertissement"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Yesterday"
msgstr "Hier"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You can not enter negative quantities."
msgstr "Vous ne pouvez pas saisir des quantités négatives."

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid "You cannot cancel a Repair Order that's already been completed"
msgstr "Vous ne pouvez pas annuler un ordre de réparation qui est déjà achevé"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You cannot create a quotation for a repair order that is already linked to an existing sale order.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""
"Vous ne pouvez pas créer un devis pour un ordre de réparation qui est déjà lié à une commande existante.\n"
"Ordre(s) de réparation concerné(s) :\n"
"%(ref_str)s"

#. module: repair
#. odoo-python
#: code:addons/repair/models/repair.py:0
msgid ""
"You need to define a customer for a repair order in order to create an associated quotation.\n"
"Concerned repair order(s):\n"
"%(ref_str)s"
msgstr ""
"Vous devez définir un client sur un ordre de réparation pour créer un devis associé. \n"
"Ordre(s) de réparation concerné(s) :\n"
"%(ref_str)s"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr "depuis l'emplacement"

#. module: repair
#: model:ir.actions.server,name:repair.action_repair_overview
msgid "stock.repair.type.overview"
msgstr "stock.repair.type.overview"
