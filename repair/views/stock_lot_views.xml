<?xml version="1.0" encoding="utf-8"?>
<odoo>
     <record id="stock_production_lot_view_form" model="ir.ui.view">
        <field name="name">stock.production.lot.view.form</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_button_box')]/button" position="before">
                <button class="oe_stat_button" name="action_view_ro" type="object" icon="fa-wrench" help="Repair Orders"
                        invisible="repair_part_count == 0 or not display_complete">
                    <div class="o_field_widget o_stat_info">
                        <div class="o_field_widget o_stat_info align-items-baseline flex-row gap-1 me-1">
                            <span class="o_stat_text">Repair Parts:</span>
                            <span class="o_stat_value">
                                <field name="repair_part_count" widget="statinfo" nolabel="1" class="mr4"/>
                            </span>
                        </div>
                    </div>
                </button>
                <button name="action_lot_open_repairs" icon="fa-wrench" class="oe_stat_button" type="object">
                    <div class="o_field_widget o_stat_info">
                        <div class="o_field_widget o_stat_info align-items-baseline flex-row gap-1 me-1">
                          <span class="o_stat_text">To Do:</span>
                          <span class="o_stat_value">
                              <field name="in_repair_count" widget="statinfo" nolabel="1" class="mr4"/>
                          </span>
                        </div>
                        <div class="o_field_widget o_stat_info align-items-baseline flex-row gap-1 me-1">
                          <span class="o_stat_text">Done:</span>
                          <span class="o_stat_value">
                              <field name="repaired_count" widget="statinfo" nolabel="1" class="mr4"/>
                          </span>
                        </div>
                    </div>
                </button>
            </xpath>
        </field>
    </record>
</odoo>
