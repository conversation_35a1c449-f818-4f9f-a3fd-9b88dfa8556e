# Changelog

All notable changes to the Universal Odoo Adapter API will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.1] - 2025-01-26

### Removed
- **Legacy Endpoints**: Removed all deprecated `/api/odoo/*` endpoints
- **Deprecation Middleware**: Removed deprecation handling middleware
- **Legacy Controller**: Removed OdooController (legacy)

### Changed
- **Clean API Structure**: Only v1 endpoints remain for cleaner API
- **Simplified Routing**: Removed complex deprecation routing logic
- **Updated Documentation**: Removed references to legacy endpoints

## [v1.0.0] - 2025-01-26

### Added
- **Universal Odoo Adapter**: Multi-version Odoo support (13, 15, 17, 18+)
- **Multi-Protocol Support**: XML-RPC, JSON-RPC, REST API protocols
- **Automatic Version Detection**: Detects Odoo version and selects optimal protocol
- **Field Mapping System**: Handles field name changes between Odoo versions
- **Version Adapters**: Dedicated adapters for each major Odoo version
- **Clean Architecture**: Domain, Application, Infrastructure, and Presentation layers
- **API Versioning**: URI-based versioning with v1 as initial version
- **Comprehensive CRUD Operations**: Create, Read, Update, Delete for all Odoo models
- **Custom Method Execution**: Execute any Odoo model method with arguments
- **Authentication Support**: Password, API Key, OAuth2, Token-based authentication
- **Swagger Documentation**: Auto-generated API documentation
- **Deprecation Management**: Proper deprecation headers and migration guidance
- **Error Handling**: Comprehensive error handling with meaningful messages
- **Logging**: Structured logging with NestJS Logger
- **Input Validation**: Request validation with class-validator
- **CORS Support**: Cross-origin resource sharing enabled
- **Environment Configuration**: Flexible configuration via environment variables

### API Endpoints (v1)
- `POST /api/v1/odoo/connect` - Connect to Odoo instance
- `GET /api/v1/odoo/version` - Get Odoo version information
- `GET /api/v1/odoo/capabilities` - Get Odoo capabilities
- `POST /api/v1/odoo/{model}/search` - Search and read records
- `POST /api/v1/odoo/{model}` - Create new record
- `PUT /api/v1/odoo/{model}` - Update existing records
- `DELETE /api/v1/odoo/{model}` - Delete records
- `POST /api/v1/odoo/{model}/execute/{method}` - Execute custom methods
- `POST /api/v1/odoo/disconnect` - Disconnect from Odoo

### Infrastructure
- **Protocol Implementations**:
  - XML-RPC Protocol (universal compatibility)
  - JSON-RPC Protocol (better performance)
  - REST API Protocol (modern Odoo versions)
- **Version Adapters**:
  - Odoo v18 Adapter (latest features)
  - Odoo v17 Adapter (OAuth2 support)
  - Odoo v15 Adapter (API key support)
  - Odoo v13 Adapter (legacy support)
- **Middleware**:
  - Deprecation middleware for legacy endpoints
  - CORS middleware
  - Validation middleware

### Documentation
- Main API documentation at `/api/docs`
- Version-specific documentation at `/api/v1/docs`
- API information endpoint at `/api`
- Health check endpoint at `/api/health`
- Comprehensive README with usage examples
- HTTP test file for API testing

### Compatibility Matrix
| Odoo Version | XML-RPC | JSON-RPC | REST API | WebSocket | Auth Methods |
|--------------|---------|----------|----------|-----------|--------------|
| 13.0         | ✅      | ✅       | ❌       | ❌        | Password     |
| 15.0         | ✅      | ✅       | 🔧       | ❌        | Password, API Key |
| 17.0         | ✅      | ✅       | 🔧       | ❌        | Password, API Key, OAuth2 |
| 18.0+        | ✅      | ✅       | ✅       | ✅        | All methods  |

### Clean API Design
- Single version (v1) API structure for simplicity
- No legacy endpoints to maintain
- Clear and consistent API paths

### Development Tools
- TypeScript support with strict type checking
- ESLint and Prettier for code quality
- Jest for testing framework
- Hot reload for development
- Production build optimization

### Future Roadmap
- GraphQL protocol support
- WebSocket real-time capabilities
- Advanced caching strategies
- Rate limiting and throttling
- Authentication and authorization
- Monitoring and metrics
- Database connection pooling
- Batch operation optimizations

---

## Version History

- **v1.0.1** (2025-01-26): Removed legacy endpoints, clean v1-only API
- **v1.0.0** (2025-01-26): Initial stable release with full Universal Odoo Adapter functionality

## Migration Guides

### API Structure
- All endpoints use `/api/v1/odoo` base URL
- Clean versioned API structure
- Response format includes `apiVersion: 'v1'` field
- No legacy endpoints to worry about

## Support

For questions, issues, or feature requests:
1. Check the API documentation
2. Review the test examples
3. Create an issue in the repository
4. Contact the development team

---

**Universal Odoo Adapter** - Built with ❤️ using NestJS and Clean Architecture
