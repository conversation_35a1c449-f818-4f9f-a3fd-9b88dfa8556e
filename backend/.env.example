# Server Configuration
PORT=3000
NODE_ENV=development

# Default Odoo Configuration (optional)
# These can be overridden via API calls
ODOO_HOST=your-odoo-instance.com
ODOO_DATABASE=your-database
ODOO_USERNAME=admin
ODOO_PASSWORD=admin
ODOO_PROTOCOL=https
ODOO_PORT=443

# Logging
LOG_LEVEL=debug

# CORS Configuration
CORS_ORIGIN=*
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_CREDENTIALS=true

# Rate Limiting (optional)
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Cache Configuration (optional)
CACHE_TTL=300
CACHE_MAX=100
