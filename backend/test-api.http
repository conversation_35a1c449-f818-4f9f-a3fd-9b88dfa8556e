### Universal Odoo Adapter API Test File
### Use this file with REST Client extension in VS Code or similar tools

@baseUrl = http://localhost:3000/api/odoo

### 1. Connect to Odoo Instance
POST {{baseUrl}}/connect
Content-Type: application/json

{
  "host": "demo.odoo.com",
  "database": "demo",
  "username": "admin",
  "password": "admin",
  "protocol": "https",
  "port": 443
}

### 2. Get Version Information
GET {{baseUrl}}/version

### 3. Get Capabilities
GET {{baseUrl}}/capabilities

### 4. Search Partners (Companies)
POST {{baseUrl}}/res.partner/search
Content-Type: application/json

{
  "domain": [["is_company", "=", true]],
  "fields": ["name", "email", "phone", "website"],
  "limit": 10,
  "order": "name ASC"
}

### 5. Search All Partners
POST {{baseUrl}}/res.partner/search
Content-Type: application/json

{
  "domain": [],
  "fields": ["name", "email", "is_company"],
  "limit": 5
}

### 6. Create a New Partner
POST {{baseUrl}}/res.partner
Content-Type: application/json

{
  "values": {
    "name": "Test Company API",
    "is_company": true,
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "website": "https://api.testcompany.com"
  }
}

### 7. Update Partner (replace {id} with actual ID from create response)
PUT {{baseUrl}}/res.partner
Content-Type: application/json

{
  "ids": [1],
  "values": {
    "phone": "+0987654321",
    "city": "API City"
  }
}

### 8. Search Products
POST {{baseUrl}}/product.product/search
Content-Type: application/json

{
  "domain": [["sale_ok", "=", true]],
  "fields": ["name", "default_code", "list_price", "type"],
  "limit": 5
}

### 9. Search Sale Orders
POST {{baseUrl}}/sale.order/search
Content-Type: application/json

{
  "domain": [["state", "in", ["sale", "done"]]],
  "fields": ["name", "partner_id", "date_order", "amount_total", "state"],
  "limit": 5,
  "order": "date_order DESC"
}

### 10. Execute Custom Method - Count Partners
POST {{baseUrl}}/res.partner/execute/search_count
Content-Type: application/json

{
  "args": [[["is_company", "=", true]]],
  "kwargs": {}
}

### 11. Execute Custom Method - Get Fields
POST {{baseUrl}}/res.partner/execute/fields_get
Content-Type: application/json

{
  "args": [["name", "email", "phone"]],
  "kwargs": {}
}

### 12. Delete Partner (replace {id} with actual ID)
DELETE {{baseUrl}}/res.partner
Content-Type: application/json

{
  "ids": [1]
}

### 13. Disconnect from Odoo
POST {{baseUrl}}/disconnect

### 14. Test with Different Odoo Instance (Odoo 13)
POST {{baseUrl}}/connect
Content-Type: application/json

{
  "host": "odoo13.example.com",
  "database": "odoo13_db",
  "username": "admin",
  "password": "admin",
  "protocol": "http",
  "port": 8069
}

### 15. Test Error Handling - Invalid Connection
POST {{baseUrl}}/connect
Content-Type: application/json

{
  "host": "invalid-host.com",
  "database": "invalid_db",
  "username": "invalid",
  "password": "invalid"
}
