# Universal Odoo Adapter - NestJS Backend

A comprehensive, production-ready NestJS backend implementing Clean Architecture with a Universal Odoo Adapter that supports multiple Odoo versions and protocols.

## 🏗️ Architecture Overview

This project implements **Clean Architecture** with the following layers:

```
src/
├── domain/                 # Business Logic & Entities
│   ├── entities/          # Domain entities (Partner, SaleOrder, etc.)
│   ├── repositories/      # Repository interfaces
│   └── value-objects/     # Value objects and types
├── application/           # Application Business Rules
│   ├── use-cases/        # Use case implementations
│   ├── dtos/             # Data Transfer Objects
│   └── interfaces/       # Application interfaces
├── infrastructure/       # External Concerns
│   ├── adapters/         # Odoo adapters and protocols
│   ├── config/           # Configuration
│   └── database/         # Database implementations
└── presentation/         # Controllers & External Interface
    ├── controllers/      # REST API controllers
    ├── guards/           # Authentication guards
    └── decorators/       # Custom decorators
```

## 🚀 Key Features

### Universal Odoo Adapter
- **Multi-Version Support**: Automatically detects and adapts to Odoo versions 13, 15, 17, 18+
- **Multi-Protocol**: Supports XML-RPC, JSON-RPC, REST API, and future protocols
- **Intelligent Selection**: Automatically selects the optimal protocol based on capabilities
- **Field Mapping**: Handles field name changes between versions
- **Graceful Fallback**: Falls back to XML-RPC if modern protocols are unavailable

### Supported Protocols
1. **XML-RPC** - Universal compatibility (all Odoo versions)
2. **JSON-RPC** - Better performance for modern versions
3. **REST API** - Native support in Odoo 18+ (via modules)
4. **GraphQL** - Future support via modules
5. **WebSocket** - Real-time capabilities in Odoo 18+

### Authentication Methods
- Password authentication (all versions)
- API Key authentication (Odoo 15+)
- OAuth2 (Odoo 16+)
- Token-based authentication (Odoo 18+)

## 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd backend

# Install dependencies
npm install

# Start development server
npm run start:dev
```

## 🔧 Configuration

Create a `.env` file in the root directory:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Default Odoo Configuration (optional)
ODOO_HOST=your-odoo-instance.com
ODOO_DATABASE=your-database
ODOO_USERNAME=admin
ODOO_PASSWORD=admin
ODOO_PROTOCOL=https
ODOO_PORT=443
```

## 📚 API Documentation

Once the server is running, visit:
- **Main API Docs**: http://localhost:3000/api/docs
- **v1 API Docs**: http://localhost:3000/api/v1/docs
- **API Information**: http://localhost:3000/api/v1/info
- **Health Check**: http://localhost:3000/api/v1/info/health

## 🔄 API Versioning

This API uses **URI versioning** with the following structure:
- **Current Version**: `v1`
- **Base URL**: `http://localhost:3000/api/v1/odoo`

### Version History
- **v1** (2025-01-26): Initial stable release with full Odoo adapter functionality

### Main Endpoints

#### Connection Management
```http
POST /api/v1/odoo/connect
GET  /api/v1/odoo/version
GET  /api/v1/odoo/capabilities
POST /api/v1/odoo/disconnect
```

#### CRUD Operations
```http
POST /api/v1/odoo/{model}/search    # Search & read records
POST /api/v1/odoo/{model}           # Create record
PUT  /api/v1/odoo/{model}           # Update records
DELETE /api/v1/odoo/{model}         # Delete records
```

#### Custom Methods
```http
POST /api/v1/odoo/{model}/execute/{method}
```

## 🔨 Usage Examples

### Basic Connection
```typescript
// Connect to Odoo
const response = await fetch('http://localhost:3000/api/v1/odoo/connect', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    host: 'your-odoo-instance.com',
    database: 'your-database',
    username: 'admin',
    password: 'admin',
    protocol: 'https'
  })
});
```

### Search Partners
```typescript
const partners = await fetch('http://localhost:3000/api/v1/odoo/res.partner/search', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    domain: [['is_company', '=', true]],
    fields: ['name', 'email', 'phone'],
    limit: 10
  })
});
```

### Create Record
```typescript
const newPartner = await fetch('http://localhost:3000/api/v1/odoo/res.partner', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    values: {
      name: 'Test Company',
      is_company: true,
      email: '<EMAIL>'
    }
  })
});
```

## 🧪 Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## 🏃‍♂️ Development

```bash
# Development mode with hot reload
npm run start:dev

# Production build
npm run build

# Production mode
npm run start:prod
```

## 🔄 Version Compatibility

| Odoo Version | XML-RPC | JSON-RPC | REST API | WebSocket | Auth Methods |
|--------------|---------|----------|----------|-----------|--------------|
| 13.0         | ✅      | ✅       | ❌       | ❌        | Password     |
| 15.0         | ✅      | ✅       | 🔧       | ❌        | Password, API Key |
| 17.0         | ✅      | ✅       | 🔧       | ❌        | Password, API Key, OAuth2 |
| 18.0+        | ✅      | ✅       | ✅       | ✅        | All methods  |

✅ Native support | 🔧 Via modules | ❌ Not available

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the [API documentation](http://localhost:3000/api/docs)
2. Review the [examples](./test-api.http)
3. Create an issue in the repository

---

**Built with ❤️ using NestJS and Clean Architecture principles**
