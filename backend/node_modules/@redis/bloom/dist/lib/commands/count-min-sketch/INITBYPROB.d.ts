import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, SimpleStringReply } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Initialize a Count-Min Sketch using error rate and probability parameters
     * @param parser - The command parser
     * @param key - The name of the sketch
     * @param error - Estimate error, as a decimal between 0 and 1
     * @param probability - The desired probability for inflated count, as a decimal between 0 and 1
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, error: number, probability: number) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=INITBYPROB.d.ts.map