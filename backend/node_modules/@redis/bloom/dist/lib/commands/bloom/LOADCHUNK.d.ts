import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, SimpleStringReply } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Restores a Bloom Filter chunk previously saved using SCANDUMP
     * @param parser - The command parser
     * @param key - The name of the Bloom filter to restore
     * @param iterator - Iterator value from the SCANDUMP command
     * @param chunk - Data chunk from the SCANDUMP command
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, iterator: number, chunk: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=LOADCHUNK.d.ts.map