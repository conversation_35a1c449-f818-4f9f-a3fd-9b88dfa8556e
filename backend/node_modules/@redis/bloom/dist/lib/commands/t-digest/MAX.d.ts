import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Returns the maximum value from a t-digest sketch
     * @param parser - The command parser
     * @param key - The name of the t-digest sketch
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: {
        2: (reply: import("@redis/client/dist/lib/RESP/types").BlobStringReply<string>, preserve?: any, typeMapping?: import("@redis/client/dist/lib/RESP/types").TypeMapping | undefined) => import("@redis/client/dist/lib/RESP/types").DoubleReply<number>;
        3: () => import("@redis/client/dist/lib/RESP/types").DoubleReply<number>;
    };
};
export default _default;
//# sourceMappingURL=MAX.d.ts.map