import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns the number of elements in the sorted set between the lexicographical range specified by min and max.
     * @param parser - The Redis command parser.
     * @param key - Key of the sorted set.
     * @param min - Minimum lexicographical value (inclusive).
     * @param max - Maximum lexicographical value (inclusive).
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, min: RedisArgument, max: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ZLEXCOUNT.d.ts.map