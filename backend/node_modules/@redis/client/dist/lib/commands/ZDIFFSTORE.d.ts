import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Computes the difference between the first and all successive sorted sets and stores it in a new key.
     * @param parser - The Redis command parser.
     * @param destination - Destination key where the result will be stored.
     * @param inputKeys - Keys of the sorted sets to find the difference between.
     */
    readonly parseCommand: (this: void, parser: CommandParser, destination: RedisArgument, inputKeys: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ZDIFFSTORE.d.ts.map