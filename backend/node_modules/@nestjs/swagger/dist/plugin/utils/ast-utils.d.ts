import { DocComment, DocNode } from '@microsoft/tsdoc';
import * as ts from 'typescript';
import { Decorator, Node, ObjectFlags, Type, TypeChecker, TypeFlags, TypeFormatFlags, UnionTypeNode } from 'typescript';
export declare function renderDocNode(docNode: DocNode): string;
export declare function isArray(type: Type): boolean;
export declare function getTypeArguments(type: Type): any;
export declare function isBoolean(type: Type): boolean;
export declare function isString(type: Type): boolean;
export declare function isStringLiteral(type: Type): boolean;
export declare function isStringMapping(type: Type): boolean;
export declare function isNumber(type: Type): boolean;
export declare function isBigInt(type: Type): boolean;
export declare function isInterface(type: Type): boolean;
export declare function isEnum(type: Type): boolean;
export declare function isEnumLiteral(type: Type): boolean;
export declare function hasFlag(type: Type, flag: TypeFlags): boolean;
export declare function hasObjectFlag(type: Type, flag: ObjectFlags): boolean;
export declare function getText(type: Type, typeChecker: TypeChecker, enclosingNode?: Node, typeFormatFlags?: TypeFormatFlags): string;
export declare function getDefaultTypeFormatFlags(enclosingNode: Node): number;
export declare function getDocComment(node: Node): DocComment;
export declare function getMainCommentOfNode(node: Node): string;
export declare function parseCommentDocValue(docValue: string, type: ts.Type): string;
export declare function getTsDocTagsOfNode(node: Node, typeChecker: TypeChecker): any;
export declare function getTsDocErrorsOfNode(node: Node): any[];
export declare function getDecoratorArguments(decorator: Decorator): any[] | ts.NodeArray<ts.Expression>;
export declare function getDecoratorName(decorator: Decorator): string;
export declare function findNullableTypeFromUnion(typeNode: UnionTypeNode, typeChecker: TypeChecker): ts.TypeNode;
export declare function createBooleanLiteral(factory: ts.NodeFactory, flag: boolean): ts.BooleanLiteral;
export declare function createPrimitiveLiteral(factory: ts.NodeFactory, item: unknown, typeOfItem?: "string" | "number" | "bigint" | "boolean" | "symbol" | "undefined" | "object" | "function"): ts.StringLiteral | ts.NumericLiteral | ts.PrefixUnaryExpression | ts.BooleanLiteral;
export declare function createLiteralFromAnyValue(factory: ts.NodeFactory, item: unknown): any;
