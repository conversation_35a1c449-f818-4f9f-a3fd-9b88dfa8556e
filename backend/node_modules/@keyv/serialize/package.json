{"name": "@keyv/serialize", "version": "1.1.0", "description": "Serialization for Keyv", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "keywords": ["keyv", "serialize", "key", "value", "store"], "author": "<PERSON> <<EMAIL>> (https://jaredwray.com)", "license": "MIT", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "homepage": "https://github.com/jaredwray/keyv", "devDependencies": {"@vitest/coverage-v8": "^3.2.4", "rimraf": "^6.0.1", "tsd": "^0.32.0", "typescript": "^5.8.3", "vitest": "^3.2.4", "xo": "^1.2.1", "@keyv/test-suite": "^2.0.9", "keyv": "^5.3.4"}, "tsd": {"directory": "test"}, "files": ["dist", "LICENSE"], "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest --run --sequence.setupFiles=list --coverage", "clean": "rimraf ./node_modules ./coverage ./dist"}}