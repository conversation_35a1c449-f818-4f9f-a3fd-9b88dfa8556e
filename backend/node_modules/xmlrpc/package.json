{"name": "xmlrpc", "description": "A pure JavaScript XML-RPC client and server.", "keywords": ["xml-rpc", "xmlrpc", "xml", "rpc"], "version": "1.3.2", "preferGlobal": false, "homepage": "https://github.com/baalexander/node-xmlrpc", "author": "<PERSON> <<EMAIL>> (https://github.com/baalexander)", "repository": {"type": "git", "url": "git://github.com/baalexander/node-xmlrpc.git"}, "bugs": {"url": "https://github.com/baalexander/node-xmlrpc/issues"}, "directories": {"lib": "./lib"}, "main": "./lib/xmlrpc.js", "dependencies": {"sax": "1.2.x", "xmlbuilder": "8.2.x"}, "devDependencies": {"vows": "0.7.x"}, "scripts": {"test": "vows 'test/*.js'"}, "engines": {"node": ">=0.8", "npm": ">=1.0.0"}, "license": "MIT"}