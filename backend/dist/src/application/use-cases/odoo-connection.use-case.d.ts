import { IOdooAdapter } from '../../domain/repositories/odoo-adapter.interface';
import { OdooConnectionConfig, SearchReadOptions } from '../../domain/value-objects/odoo-connection-config';
export declare class OdooConnectionUseCase {
    private readonly odooAdapter;
    private readonly logger;
    constructor(odooAdapter: IOdooAdapter);
    connect(config: OdooConnectionConfig): Promise<void>;
    getVersionInfo(): Promise<import("../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null>;
    getCapabilities(): Promise<import("../../domain/value-objects/odoo-connection-config").OdooCapabilities | null>;
    searchRead<T = any>(model: string, domain?: any[], options?: SearchReadOptions): Promise<T[]>;
    create<T = any>(model: string, values: Partial<T>): Promise<number>;
    update<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean>;
    delete(model: string, ids: number[]): Promise<boolean>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    disconnect(): Promise<void>;
}
