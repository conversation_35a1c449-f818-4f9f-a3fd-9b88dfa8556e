import { OdooConnectionConfig, SearchReadOptions } from '../../domain/value-objects/odoo-connection-config';
import { OdooSessionService } from '../../infrastructure/adapters/odoo/odoo-session.service';
export declare class OdooConnectionUseCase {
    private readonly odooSessionService;
    private readonly logger;
    constructor(odooSessionService: OdooSessionService);
    connect(config: OdooConnectionConfig): Promise<void>;
    getVersionInfo(): Promise<any>;
    getCapabilities(): Promise<any>;
    searchRead<T = any>(model: string, domain?: any[], options?: SearchReadOptions): Promise<T[]>;
    create<T = any>(model: string, values: Partial<T>): Promise<number>;
    update<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean>;
    delete(model: string, ids: number[]): Promise<boolean>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    disconnect(): Promise<void>;
}
