import { OdooConnectionConfig, SearchReadOptions } from '../../domain/value-objects/odoo-connection-config';
import { OdooConnectionPoolService } from '../../infrastructure/adapters/odoo/odoo-connection-pool.service';
import { UserContextService } from '../../infrastructure/adapters/odoo/user-context.service';
export declare class OdooConnectionUseCase {
    private readonly connectionPoolService;
    private readonly userContextService;
    private readonly logger;
    constructor(connectionPoolService: OdooConnectionPoolService, userContextService: UserContextService);
    connect(config: OdooConnectionConfig): Promise<void>;
    getVersionInfo(): Promise<import("../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null>;
    getCapabilities(): Promise<import("../../domain/value-objects/odoo-connection-config").OdooCapabilities | null>;
    searchRead<T = any>(model: string, domain?: any[], options?: SearchReadOptions): Promise<T[]>;
    create<T = any>(model: string, values: Partial<T>): Promise<number>;
    update<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean>;
    delete(model: string, ids: number[]): Promise<boolean>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    disconnect(): Promise<void>;
    getPoolStats(): {
        size: number;
        maxSize: number;
        connections: {
            key: string;
            lastUsed: Date;
            host: string;
            database: string;
        }[];
    };
    disconnectUser(): Promise<void>;
    private getAdapter;
    private extractUserFromRequest;
}
