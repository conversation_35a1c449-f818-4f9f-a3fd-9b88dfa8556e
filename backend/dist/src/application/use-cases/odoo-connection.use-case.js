"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OdooConnectionUseCase_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooConnectionUseCase = void 0;
const common_1 = require("@nestjs/common");
const odoo_connection_pool_service_1 = require("../../infrastructure/adapters/odoo/odoo-connection-pool.service");
const user_context_service_1 = require("../../infrastructure/adapters/odoo/user-context.service");
let OdooConnectionUseCase = OdooConnectionUseCase_1 = class OdooConnectionUseCase {
    connectionPoolService;
    userContextService;
    logger = new common_1.Logger(OdooConnectionUseCase_1.name);
    constructor(connectionPoolService, userContextService) {
        this.connectionPoolService = connectionPoolService;
        this.userContextService = userContextService;
    }
    async connect(config) {
        try {
            this.userContextService.setUserContext(config);
            const adapter = await this.connectionPoolService.getConnection(this.userContextService.getUserContext());
            this.logger.log(`Successfully connected to Odoo at ${config.host} for user: ${this.userContextService.getUserId()}`);
        }
        catch (error) {
            this.logger.error('Failed to connect to Odoo', error);
            throw error;
        }
    }
    async getVersionInfo() {
        try {
            const adapter = await this.getAdapter();
            return adapter.getVersionInfo();
        }
        catch (error) {
            this.logger.error('Failed to get version info', error);
            throw error;
        }
    }
    async getCapabilities() {
        try {
            const adapter = await this.getAdapter();
            return adapter.getCapabilities();
        }
        catch (error) {
            this.logger.error('Failed to get capabilities', error);
            throw error;
        }
    }
    async searchRead(model, domain, options) {
        try {
            const adapter = await this.getAdapter();
            return await adapter.searchRead(model, domain, options);
        }
        catch (error) {
            this.logger.error(`Failed to search ${model}`, error);
            throw error;
        }
    }
    async create(model, values) {
        try {
            const adapter = await this.getAdapter();
            return await adapter.create(model, values);
        }
        catch (error) {
            this.logger.error(`Failed to create ${model}`, error);
            throw error;
        }
    }
    async update(model, ids, values) {
        try {
            const adapter = await this.getAdapter();
            return await adapter.write(model, ids, values);
        }
        catch (error) {
            this.logger.error(`Failed to update ${model}`, error);
            throw error;
        }
    }
    async delete(model, ids) {
        try {
            const adapter = await this.getAdapter();
            return await adapter.unlink(model, ids);
        }
        catch (error) {
            this.logger.error(`Failed to delete ${model}`, error);
            throw error;
        }
    }
    async execute(model, method, args, kwargs) {
        try {
            const adapter = await this.getAdapter();
            return await adapter.execute(model, method, args, kwargs);
        }
        catch (error) {
            this.logger.error(`Failed to execute ${model}.${method}`, error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.userContextService.hasUserContext()) {
                await this.connectionPoolService.removeConnection(this.userContextService.getUserContext());
                this.logger.log(`Disconnected user: ${this.userContextService.getUserId()} from Odoo`);
            }
        }
        catch (error) {
            this.logger.error('Failed to disconnect from Odoo', error);
            throw error;
        }
    }
    getPoolStats() {
        return this.connectionPoolService.getPoolStats();
    }
    getPoolMetrics() {
        return this.connectionPoolService.getPoolMetrics();
    }
    async healthCheck() {
        return await this.connectionPoolService.healthCheck();
    }
    async executeBatch(operations) {
        return await this.connectionPoolService.executeBatchOperations(operations);
    }
    async cleanupStaleConnections(maxAgeMinutes = 60) {
        return await this.connectionPoolService.cleanupStaleConnections(maxAgeMinutes);
    }
    async refreshExpiringConnections(refreshThresholdMinutes = 25) {
        return await this.connectionPoolService.refreshExpiringConnections(refreshThresholdMinutes);
    }
    async disconnectUser() {
        try {
            if (this.userContextService.hasUserContext()) {
                await this.connectionPoolService.removeUserConnections(this.userContextService.getUserId());
                this.logger.log(`Disconnected all connections for user: ${this.userContextService.getUserId()}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to disconnect user connections', error);
            throw error;
        }
    }
    async getAdapter() {
        if (!this.userContextService.hasUserContext()) {
            const user = this.extractUserFromRequest();
            const cachedConnection = this.connectionPoolService.findUserConnection(user.id);
            if (cachedConnection) {
                this.userContextService.setUserContext(cachedConnection.connectionConfig);
                this.logger.debug(`Restored user context for user: ${user.id}`);
            }
            else {
                throw new Error('User context not set and no existing connection found. Call connect() first.');
            }
        }
        return await this.connectionPoolService.getConnection(this.userContextService.getUserContext());
    }
    extractUserFromRequest() {
        const userId = this.userContextService['request']?.headers?.['x-user-id'];
        const username = this.userContextService['request']?.headers?.['x-username'];
        if (userId && username) {
            return { id: userId, username };
        }
        if (process.env.NODE_ENV === 'development') {
            return { id: 'anon_dev', username: 'anonymous' };
        }
        throw new Error('No user found in request');
    }
};
exports.OdooConnectionUseCase = OdooConnectionUseCase;
exports.OdooConnectionUseCase = OdooConnectionUseCase = OdooConnectionUseCase_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [odoo_connection_pool_service_1.OdooConnectionPoolService,
        user_context_service_1.UserContextService])
], OdooConnectionUseCase);
//# sourceMappingURL=odoo-connection.use-case.js.map