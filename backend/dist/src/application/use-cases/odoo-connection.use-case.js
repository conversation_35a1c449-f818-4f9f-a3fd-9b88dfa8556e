"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var OdooConnectionUseCase_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooConnectionUseCase = void 0;
const common_1 = require("@nestjs/common");
let OdooConnectionUseCase = OdooConnectionUseCase_1 = class OdooConnectionUseCase {
    odooAdapter;
    logger = new common_1.Logger(OdooConnectionUseCase_1.name);
    constructor(odooAdapter) {
        this.odooAdapter = odooAdapter;
    }
    async connect(config) {
        try {
            this.odooAdapter.setConnectionConfig(config);
            await this.odooAdapter.connect();
            await this.odooAdapter.authenticate();
            this.logger.log(`Successfully connected to Odoo at ${config.host}`);
        }
        catch (error) {
            this.logger.error('Failed to connect to Odoo', error);
            throw error;
        }
    }
    async getVersionInfo() {
        return this.odooAdapter.getVersionInfo();
    }
    async getCapabilities() {
        return this.odooAdapter.getCapabilities();
    }
    async searchRead(model, domain, options) {
        try {
            return await this.odooAdapter.searchRead(model, domain, options);
        }
        catch (error) {
            this.logger.error(`Failed to search ${model}`, error);
            throw error;
        }
    }
    async create(model, values) {
        try {
            return await this.odooAdapter.create(model, values);
        }
        catch (error) {
            this.logger.error(`Failed to create ${model}`, error);
            throw error;
        }
    }
    async update(model, ids, values) {
        try {
            return await this.odooAdapter.write(model, ids, values);
        }
        catch (error) {
            this.logger.error(`Failed to update ${model}`, error);
            throw error;
        }
    }
    async delete(model, ids) {
        try {
            return await this.odooAdapter.unlink(model, ids);
        }
        catch (error) {
            this.logger.error(`Failed to delete ${model}`, error);
            throw error;
        }
    }
    async execute(model, method, args, kwargs) {
        try {
            return await this.odooAdapter.execute(model, method, args, kwargs);
        }
        catch (error) {
            this.logger.error(`Failed to execute ${model}.${method}`, error);
            throw error;
        }
    }
    async disconnect() {
        await this.odooAdapter.disconnect();
        this.logger.log('Disconnected from Odoo');
    }
};
exports.OdooConnectionUseCase = OdooConnectionUseCase;
exports.OdooConnectionUseCase = OdooConnectionUseCase = OdooConnectionUseCase_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('IOdooAdapter')),
    __metadata("design:paramtypes", [Object])
], OdooConnectionUseCase);
//# sourceMappingURL=odoo-connection.use-case.js.map