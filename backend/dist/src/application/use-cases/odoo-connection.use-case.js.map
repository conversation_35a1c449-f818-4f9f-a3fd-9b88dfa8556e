{"version": 3, "file": "odoo-connection.use-case.js", "sourceRoot": "", "sources": ["../../../../src/application/use-cases/odoo-connection.use-case.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AAKpD,kGAA6F;AAGtF,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAIb;IAHF,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAEjE,YACmB,kBAAsC;QAAtC,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,MAA4B;QACxC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,UAAU,CACd,KAAa,EACb,MAAc,EACd,OAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;YACrD,OAAO,MAAM,OAAO,CAAC,UAAU,CAAI,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAU,KAAa,EAAE,MAAkB;QACrD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;YACrD,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CACV,KAAa,EACb,GAAa,EACb,MAAkB;QAElB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;YACrD,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,KAAa,EAAE,GAAa;QACvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;YACrD,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CACX,KAAa,EACb,MAAc,EACd,IAAW,EACX,MAAY;QAEZ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;YACrD,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,KAAK,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAC5C,CAAC;CACF,CAAA;AA5FY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;qCAK4B,yCAAkB;GAJ9C,qBAAqB,CA4FjC"}