{"version": 3, "file": "odoo-connection.use-case.js", "sourceRoot": "", "sources": ["../../../../src/application/use-cases/odoo-connection.use-case.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AAKpD,kHAA4G;AAC5G,kGAA6F;AAGtF,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAIb;IACA;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAEjE,YACmB,qBAAgD,EAChD,kBAAsC;QADtC,0BAAqB,GAArB,qBAAqB,CAA2B;QAChD,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,MAA4B;QACxC,IAAI,CAAC;YAEH,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAG/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAC5D,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CACzC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,MAAM,CAAC,IAAI,cAAc,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACvH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,OAAO,CAAC,cAAc,EAAE,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,OAAO,CAAC,eAAe,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CACd,KAAa,EACb,MAAc,EACd,OAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,UAAU,CAAI,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAU,KAAa,EAAE,MAAkB;QACrD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CACV,KAAa,EACb,GAAa,EACb,MAAkB;QAElB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,KAAa,EAAE,GAAa;QACvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CACX,KAAa,EACb,MAAc,EACd,IAAW,EACX,MAAY;QAEZ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,KAAK,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAC/C,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CACzC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACzF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,YAAY;QACV,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,CAAC;IACnD,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CACpD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CACpC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACnG,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC;YAE9C,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEhF,IAAI,gBAAgB,EAAE,CAAC;gBAErB,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;gBAC1E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;YAClG,CAAC;QACH,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,CACnD,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CACzC,CAAC;IACJ,CAAC;IAKO,sBAAsB;QAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,WAAW,CAAW,CAAC;QACpF,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,YAAY,CAAW,CAAC;QAEvF,IAAI,MAAM,IAAI,QAAQ,EAAE,CAAC;YACvB,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAClC,CAAC;QAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;QACnD,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;CACF,CAAA;AA5LY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;qCAK+B,wDAAyB;QAC5B,yCAAkB;GAL9C,qBAAqB,CA4LjC"}