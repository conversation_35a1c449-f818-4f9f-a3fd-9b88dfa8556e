"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteRecordDto = exports.UpdateRecordDto = exports.CreateRecordDto = exports.SearchReadDto = exports.OdooConnectionDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class OdooConnectionDto {
    host;
    database;
    username;
    password;
    port;
    protocol;
}
exports.OdooConnectionDto = OdooConnectionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Odoo server host' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OdooConnectionDto.prototype, "host", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Database name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OdooConnectionDto.prototype, "database", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Username' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OdooConnectionDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Password' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OdooConnectionDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Port number', default: 80 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], OdooConnectionDto.prototype, "port", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Protocol', enum: ['http', 'https'], default: 'http' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['http', 'https']),
    __metadata("design:type", String)
], OdooConnectionDto.prototype, "protocol", void 0);
class SearchReadDto {
    domain;
    fields;
    limit;
    offset;
    order;
}
exports.SearchReadDto = SearchReadDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Domain filter', type: [Array] }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], SearchReadDto.prototype, "domain", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Fields to retrieve', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], SearchReadDto.prototype, "fields", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Limit number of records' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SearchReadDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Offset for pagination' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SearchReadDto.prototype, "offset", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Order by field' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SearchReadDto.prototype, "order", void 0);
class CreateRecordDto {
    values;
}
exports.CreateRecordDto = CreateRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Record values', type: Object }),
    __metadata("design:type", Object)
], CreateRecordDto.prototype, "values", void 0);
class UpdateRecordDto {
    ids;
    values;
}
exports.UpdateRecordDto = UpdateRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Record IDs', type: [Number] }),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], UpdateRecordDto.prototype, "ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Values to update', type: Object }),
    __metadata("design:type", Object)
], UpdateRecordDto.prototype, "values", void 0);
class DeleteRecordDto {
    ids;
}
exports.DeleteRecordDto = DeleteRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Record IDs to delete', type: [Number] }),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], DeleteRecordDto.prototype, "ids", void 0);
//# sourceMappingURL=odoo-connection.dto.js.map