"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const api_versioning_config_1 = require("./infrastructure/config/api-versioning.config");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.enableCors();
    app.setGlobalPrefix('api');
    app.enableVersioning(api_versioning_config_1.API_VERSIONING_CONFIG);
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Universal Odoo Adapter API')
        .setDescription('A comprehensive API for connecting to multiple Odoo versions with automatic protocol selection')
        .setVersion('1.0')
        .addTag('API Information')
        .addTag('Odoo Integration v1')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document);
    const v1Config = new swagger_1.DocumentBuilder()
        .setTitle('Universal Odoo Adapter API v1')
        .setDescription('Version 1 of the Universal Odoo Adapter API')
        .setVersion('1.0')
        .addTag('Odoo Integration v1')
        .build();
    const v1Document = swagger_1.SwaggerModule.createDocument(app, v1Config, {
        include: [],
    });
    swagger_1.SwaggerModule.setup('api/v1/docs', app, v1Document);
    const port = process.env.PORT ?? 3000;
    await app.listen(port);
    console.log(`🚀 Universal Odoo Adapter API is running on: http://localhost:${port}`);
    console.log(`📚 API Documentation:`);
    console.log(`   • Main docs: http://localhost:${port}/api/docs`);
    console.log(`   • v1 docs:   http://localhost:${port}/api/v1/docs`);
    console.log(`📋 API Endpoints:`);
    console.log(`   • API info:  http://localhost:${port}/api/v1/info`);
    console.log(`   • Health:    http://localhost:${port}/api/v1/info/health`);
    console.log(`   • v1 base:   http://localhost:${port}/api/v1/odoo`);
}
bootstrap();
//# sourceMappingURL=main.js.map