"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooModule = void 0;
const common_1 = require("@nestjs/common");
const xmlrpc_protocol_1 = require("./adapters/protocols/xmlrpc/xmlrpc-protocol");
const jsonrpc_protocol_1 = require("./adapters/protocols/jsonrpc/jsonrpc-protocol");
const rest_protocol_1 = require("./adapters/protocols/rest/rest-protocol");
const odoo_v18_adapter_1 = require("./adapters/odoo/version-adapters/odoo-v18-adapter");
const odoo_v17_adapter_1 = require("./adapters/odoo/version-adapters/odoo-v17-adapter");
const odoo_v15_adapter_1 = require("./adapters/odoo/version-adapters/odoo-v15-adapter");
const odoo_v13_adapter_1 = require("./adapters/odoo/version-adapters/odoo-v13-adapter");
const universal_odoo_adapter_1 = require("./adapters/odoo/universal-odoo-adapter");
const odoo_session_service_1 = require("./adapters/odoo/odoo-session.service");
const odoo_connection_use_case_1 = require("../application/use-cases/odoo-connection.use-case");
const odoo_v1_controller_1 = require("../presentation/controllers/v1/odoo-v1.controller");
const api_version_controller_1 = require("../presentation/controllers/api-version.controller");
const ODOO_ADAPTER_TOKEN = 'IOdooAdapter';
let OdooModule = class OdooModule {
};
exports.OdooModule = OdooModule;
exports.OdooModule = OdooModule = __decorate([
    (0, common_1.Module)({
        providers: [
            xmlrpc_protocol_1.XmlRpcProtocol,
            jsonrpc_protocol_1.JsonRpcProtocol,
            rest_protocol_1.RestApiProtocol,
            odoo_v18_adapter_1.OdooV18Adapter,
            odoo_v17_adapter_1.OdooV17Adapter,
            odoo_v15_adapter_1.OdooV15Adapter,
            odoo_v13_adapter_1.OdooV13Adapter,
            universal_odoo_adapter_1.UniversalOdooAdapter,
            odoo_session_service_1.OdooSessionService,
            {
                provide: ODOO_ADAPTER_TOKEN,
                useClass: universal_odoo_adapter_1.UniversalOdooAdapter,
            },
            odoo_connection_use_case_1.OdooConnectionUseCase,
        ],
        controllers: [
            api_version_controller_1.ApiVersionController,
            odoo_v1_controller_1.OdooV1Controller,
        ],
        exports: [
            ODOO_ADAPTER_TOKEN,
            odoo_connection_use_case_1.OdooConnectionUseCase,
            universal_odoo_adapter_1.UniversalOdooAdapter,
            odoo_session_service_1.OdooSessionService,
        ],
    })
], OdooModule);
//# sourceMappingURL=odoo.module.js.map