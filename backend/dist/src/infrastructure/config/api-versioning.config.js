"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiVersioningService = exports.VERSION_REGISTRY = exports.API_VERSIONS = exports.API_VERSIONING_CONFIG = void 0;
const common_1 = require("@nestjs/common");
exports.API_VERSIONING_CONFIG = {
    type: common_1.VersioningType.URI,
    prefix: 'v',
    defaultVersion: '1',
};
exports.API_VERSIONS = {
    V1: '1',
};
exports.VERSION_REGISTRY = {
    [exports.API_VERSIONS.V1]: {
        version: 'v1',
        releaseDate: '2025-01-26',
        status: 'stable',
        features: [
            'Universal Odoo Adapter with multi-version support',
            'Multi-protocol support (XML-RPC, JSON-RPC, REST)',
            'Automatic version detection and protocol selection',
            'Field mapping between Odoo versions',
            'CRUD operations for all Odoo models',
            'Custom method execution',
            'Comprehensive error handling',
            'Swagger documentation',
        ],
    },
};
class ApiVersioningService {
    static getCurrentVersion() {
        return exports.API_VERSIONS.V1;
    }
    static getAvailableVersions() {
        return Object.values(exports.API_VERSIONS);
    }
    static getVersionInfo(version) {
        return exports.VERSION_REGISTRY[version];
    }
    static isVersionSupported(version) {
        return Object.values(exports.API_VERSIONS).includes(version);
    }
    static getLatestVersion() {
        const versions = this.getAvailableVersions();
        return versions[versions.length - 1];
    }
    static compareVersions(v1, v2) {
        const version1 = parseInt(v1);
        const version2 = parseInt(v2);
        return version1 - version2;
    }
}
exports.ApiVersioningService = ApiVersioningService;
//# sourceMappingURL=api-versioning.config.js.map