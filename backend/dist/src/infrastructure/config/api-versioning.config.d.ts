import { VersioningType } from '@nestjs/common';
export declare const API_VERSIONING_CONFIG: {
    readonly type: VersioningType.URI;
    readonly prefix: "v";
    readonly defaultVersion: "1";
};
export declare const API_VERSIONS: {
    readonly V1: "1";
};
export type ApiVersion = (typeof API_VERSIONS)[keyof typeof API_VERSIONS];
export interface VersionInfo {
    version: string;
    releaseDate: string;
    status: 'stable' | 'beta' | 'deprecated';
    features: string[];
    breakingChanges?: string[];
    migrationGuide?: string;
}
export declare const VERSION_REGISTRY: Record<ApiVersion, VersionInfo>;
export declare class ApiVersioningService {
    static getCurrentVersion(): ApiVersion;
    static getAvailableVersions(): ApiVersion[];
    static getVersionInfo(version: ApiVersion): VersionInfo | undefined;
    static isVersionSupported(version: string): boolean;
    static getLatestVersion(): ApiVersion;
    static compareVersions(v1: string, v2: string): number;
}
