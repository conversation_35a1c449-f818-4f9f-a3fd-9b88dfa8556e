"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var JsonRpcProtocol_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonRpcProtocol = void 0;
const common_1 = require("@nestjs/common");
const odoo_connection_config_1 = require("../../../../domain/value-objects/odoo-connection-config");
let JsonRpcProtocol = JsonRpcProtocol_1 = class JsonRpcProtocol {
    logger = new common_1.Logger(JsonRpcProtocol_1.name);
    type = odoo_connection_config_1.ProtocolType.JSONRPC;
    supportedMethods = ['execute', 'execute_kw'];
    sessionId = null;
    baseUrl;
    config;
    async connect(config) {
        this.config = config;
        const port = config.port || (config.protocol === 'https' ? 443 : 80);
        this.baseUrl = `${config.protocol}://${config.host}:${port}`;
        this.logger.log(`Connected to Odoo JSON-RPC at ${this.baseUrl}`);
    }
    async authenticate(method, credentials) {
        try {
            const response = await fetch(`${this.baseUrl}/web/session/authenticate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        db: credentials.database || this.config.database,
                        login: credentials.username || this.config.username,
                        password: credentials.password || this.config.password
                    }
                })
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await response.json();
            if (result.error) {
                throw new Error(result.error.message || 'Authentication failed');
            }
            if (!result.result || !result.result.uid) {
                throw new Error('Invalid credentials');
            }
            this.sessionId = result.result.session_id;
            const uid = result.result.uid;
            this.logger.log(`Authenticated via JSON-RPC as user ID: ${uid}`);
            return uid;
        }
        catch (error) {
            this.logger.error('JSON-RPC authentication failed', error);
            throw new Error(`Authentication failed: ${error.message}`);
        }
    }
    async execute(model, method, args, kwargs) {
        if (!this.sessionId) {
            throw new Error('Not authenticated. Call authenticate() first.');
        }
        try {
            const response = await fetch(`${this.baseUrl}/jsonrpc`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cookie': `session_id=${this.sessionId}`
                },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        service: 'object',
                        method: 'execute_kw',
                        args: [model, method, args, kwargs || {}]
                    }
                })
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await response.json();
            if (result.error) {
                throw new Error(result.error.message || 'Execute failed');
            }
            return result.result;
        }
        catch (error) {
            this.logger.error(`JSON-RPC execute failed for ${model}.${method}`, error);
            throw new Error(`Execute failed: ${error.message}`);
        }
    }
    async disconnect() {
        this.sessionId = null;
        this.logger.log('Disconnected from JSON-RPC');
    }
};
exports.JsonRpcProtocol = JsonRpcProtocol;
exports.JsonRpcProtocol = JsonRpcProtocol = JsonRpcProtocol_1 = __decorate([
    (0, common_1.Injectable)()
], JsonRpcProtocol);
//# sourceMappingURL=jsonrpc-protocol.js.map