import { IOdooProtocol } from '../../../../domain/repositories/odoo-adapter.interface';
import { OdooConnectionConfig, ProtocolType, AuthMethod } from '../../../../domain/value-objects/odoo-connection-config';
export declare class JsonRpcProtocol implements IOdooProtocol {
    private readonly logger;
    readonly type = ProtocolType.JSONRPC;
    readonly supportedMethods: string[];
    private sessionId;
    private baseUrl;
    private config;
    connect(config: OdooConnectionConfig): Promise<void>;
    authenticate(method: AuthMethod, credentials: any): Promise<number>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    disconnect(): Promise<void>;
}
