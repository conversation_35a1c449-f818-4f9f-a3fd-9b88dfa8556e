import { IOdooProtocol } from '../../../../domain/repositories/odoo-adapter.interface';
import { OdooConnectionConfig, ProtocolType, AuthMethod } from '../../../../domain/value-objects/odoo-connection-config';
export declare class XmlRpcProtocol implements IOdooProtocol {
    private readonly logger;
    readonly type = ProtocolType.XMLRPC;
    readonly supportedMethods: string[];
    private objectClient;
    private commonClient;
    private uid;
    private config;
    connect(config: OdooConnectionConfig): Promise<void>;
    authenticate(method: AuthMethod, credentials: any): Promise<number>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    disconnect(): Promise<void>;
}
