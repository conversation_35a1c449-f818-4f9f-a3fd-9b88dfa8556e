"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var XmlRpcProtocol_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.XmlRpcProtocol = void 0;
const common_1 = require("@nestjs/common");
const xmlrpc = require("xmlrpc");
const odoo_connection_config_1 = require("../../../../domain/value-objects/odoo-connection-config");
let XmlRpcProtocol = XmlRpcProtocol_1 = class XmlRpcProtocol {
    logger = new common_1.Logger(XmlRpcProtocol_1.name);
    type = odoo_connection_config_1.ProtocolType.XMLRPC;
    supportedMethods = ['execute', 'execute_kw'];
    objectClient;
    commonClient;
    uid = null;
    config;
    async connect(config) {
        this.config = config;
        const port = config.port || (config.protocol === 'https' ? 443 : 80);
        const cleanHost = config.host.replace(/^https?:\/\//, '');
        try {
            this.commonClient = xmlrpc.createClient({
                host: cleanHost,
                port,
                path: '/xmlrpc/2/common',
                basic_auth: config.protocol === 'https' ? undefined : null,
            });
            this.objectClient = xmlrpc.createClient({
                host: cleanHost,
                port,
                path: '/xmlrpc/2/object',
                basic_auth: config.protocol === 'https' ? undefined : null,
            });
            this.logger.log(`Connected to Odoo XML-RPC at ${config.protocol}://${config.host}:${port}`);
        }
        catch (error) {
            this.logger.error('Failed to connect to XML-RPC', error);
            throw new Error(`XML-RPC connection failed: ${error.message}`);
        }
    }
    async authenticate(method, credentials) {
        if (method !== odoo_connection_config_1.AuthMethod.PASSWORD) {
            throw new Error('XML-RPC only supports password authentication');
        }
        return new Promise((resolve, reject) => {
            this.commonClient.methodCall('authenticate', [
                credentials.database || this.config.database,
                credentials.username || this.config.username,
                credentials.password || this.config.password,
                {},
            ], (error, value) => {
                if (error) {
                    this.logger.error('XML-RPC authentication failed', error);
                    reject(new Error(`Authentication failed: ${error.message}`));
                }
                else if (!value) {
                    reject(new Error('Invalid credentials'));
                }
                else {
                    this.uid = value;
                    this.logger.log(`Authenticated as user ID: ${this.uid}`);
                    resolve(value);
                }
            });
        });
    }
    async execute(model, method, args, kwargs) {
        if (!this.uid) {
            throw new Error('Not authenticated. Call authenticate() first.');
        }
        return new Promise((resolve, reject) => {
            const params = [
                this.config.database,
                this.uid,
                this.config.password,
                model,
                method,
                args,
                kwargs || {},
            ];
            this.objectClient.methodCall('execute_kw', params, (error, value) => {
                if (error) {
                    this.logger.error(`XML-RPC execute failed for ${model}.${method}`, error);
                    reject(new Error(`Execute failed: ${error.message}`));
                }
                else {
                    resolve(value);
                }
            });
        });
    }
    async disconnect() {
        this.uid = null;
        this.objectClient = null;
        this.commonClient = null;
        this.logger.log('Disconnected from XML-RPC');
    }
};
exports.XmlRpcProtocol = XmlRpcProtocol;
exports.XmlRpcProtocol = XmlRpcProtocol = XmlRpcProtocol_1 = __decorate([
    (0, common_1.Injectable)()
], XmlRpcProtocol);
//# sourceMappingURL=xmlrpc-protocol.js.map