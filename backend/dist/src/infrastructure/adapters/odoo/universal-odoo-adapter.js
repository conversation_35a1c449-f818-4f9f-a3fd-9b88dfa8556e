"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UniversalOdooAdapter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalOdooAdapter = void 0;
const common_1 = require("@nestjs/common");
const odoo_connection_config_1 = require("../../../domain/value-objects/odoo-connection-config");
const xmlrpc_protocol_1 = require("../protocols/xmlrpc/xmlrpc-protocol");
const jsonrpc_protocol_1 = require("../protocols/jsonrpc/jsonrpc-protocol");
const rest_protocol_1 = require("../protocols/rest/rest-protocol");
const odoo_v18_adapter_1 = require("./version-adapters/odoo-v18-adapter");
const odoo_v17_adapter_1 = require("./version-adapters/odoo-v17-adapter");
const odoo_v15_adapter_1 = require("./version-adapters/odoo-v15-adapter");
const odoo_v13_adapter_1 = require("./version-adapters/odoo-v13-adapter");
let UniversalOdooAdapter = UniversalOdooAdapter_1 = class UniversalOdooAdapter {
    xmlRpcProtocol;
    jsonRpcProtocol;
    restApiProtocol;
    odooV18Adapter;
    odooV17Adapter;
    odooV15Adapter;
    odooV13Adapter;
    logger = new common_1.Logger(UniversalOdooAdapter_1.name);
    protocol;
    versionAdapter;
    versionInfo = null;
    capabilities = null;
    config;
    constructor(xmlRpcProtocol, jsonRpcProtocol, restApiProtocol, odooV18Adapter, odooV17Adapter, odooV15Adapter, odooV13Adapter) {
        this.xmlRpcProtocol = xmlRpcProtocol;
        this.jsonRpcProtocol = jsonRpcProtocol;
        this.restApiProtocol = restApiProtocol;
        this.odooV18Adapter = odooV18Adapter;
        this.odooV17Adapter = odooV17Adapter;
        this.odooV15Adapter = odooV15Adapter;
        this.odooV13Adapter = odooV13Adapter;
    }
    async connect() {
        if (!this.config) {
            throw new Error('Connection config not set. Call setConnectionConfig() first.');
        }
        try {
            this.versionInfo = await this.detectVersion();
            this.logger.log(`Detected Odoo version: ${this.versionInfo.series}`);
            this.versionAdapter = this.createVersionAdapter(this.versionInfo);
            this.capabilities = this.versionAdapter.capabilities;
            this.protocol = this.selectOptimalProtocol();
            this.logger.log(`Selected protocol: ${this.protocol.type}`);
            await this.protocol.connect(this.config);
        }
        catch (error) {
            this.logger.error('Failed to connect to Odoo', error);
            throw new Error(`Connection failed: ${error.message}`);
        }
    }
    async authenticate(method, credentials) {
        const authMethod = method || this.selectBestAuthMethod();
        const creds = credentials || {
            database: this.config.database,
            username: this.config.username,
            password: this.config.password,
        };
        try {
            const uid = await this.protocol.authenticate(authMethod, creds);
            this.logger.log(`Authenticated successfully with method: ${authMethod}`);
            return uid;
        }
        catch (error) {
            this.logger.error('Authentication failed', error);
            throw error;
        }
    }
    async searchRead(model, domain = [], options = {}) {
        try {
            const mappedFields = this.versionAdapter.mapFields(model, options.fields || []);
            const mappedDomain = this.versionAdapter.mapDomain(domain);
            const mappedMethod = this.versionAdapter.mapMethod(model, 'search_read');
            const result = await this.protocol.execute(model, mappedMethod, [mappedDomain], {
                fields: mappedFields.length > 0 ? mappedFields : undefined,
                limit: options.limit,
                offset: options.offset,
                order: options.order,
            });
            return this.versionAdapter.handleResponse(result);
        }
        catch (error) {
            this.logger.error(`searchRead failed for model ${model}`, error);
            throw error;
        }
    }
    async create(model, values) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, 'create');
            return await this.protocol.execute(model, mappedMethod, [values]);
        }
        catch (error) {
            this.logger.error(`create failed for model ${model}`, error);
            throw error;
        }
    }
    async write(model, ids, values) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, 'write');
            return await this.protocol.execute(model, mappedMethod, [ids, values]);
        }
        catch (error) {
            this.logger.error(`write failed for model ${model}`, error);
            throw error;
        }
    }
    async unlink(model, ids) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, 'unlink');
            return await this.protocol.execute(model, mappedMethod, [ids]);
        }
        catch (error) {
            this.logger.error(`unlink failed for model ${model}`, error);
            throw error;
        }
    }
    async execute(model, method, args, kwargs) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, method);
            const result = await this.protocol.execute(model, mappedMethod, args, kwargs);
            return this.versionAdapter.handleResponse(result);
        }
        catch (error) {
            this.logger.error(`execute failed for ${model}.${method}`, error);
            throw error;
        }
    }
    getVersionInfo() {
        return this.versionInfo;
    }
    getCapabilities() {
        return this.capabilities;
    }
    async disconnect() {
        if (this.protocol) {
            await this.protocol.disconnect();
        }
        this.logger.log('Disconnected from Odoo');
    }
    setConnectionConfig(config) {
        this.config = config;
    }
    async detectVersion() {
        try {
            return await this.detectVersionViaXmlRpc();
        }
        catch (error) {
            try {
                return await this.detectVersionViaJsonRpc();
            }
            catch (error) {
                return await this.detectVersionViaHttp();
            }
        }
    }
    async detectVersionViaXmlRpc() {
        const xmlrpc = require('xmlrpc');
        const port = this.config.port || (this.config.protocol === 'https' ? 443 : 80);
        const cleanHost = this.config.host.replace(/^https?:\/\//, '');
        const client = xmlrpc.createClient({
            host: cleanHost,
            port,
            path: '/xmlrpc/2/common',
        });
        return new Promise((resolve, reject) => {
            client.methodCall('version', [], (error, value) => {
                if (error) {
                    reject(error);
                }
                else {
                    resolve(this.parseVersionInfo(value));
                }
            });
        });
    }
    async detectVersionViaJsonRpc() {
        const cleanHost = this.config.host.replace(/^https?:\/\//, '');
        const baseUrl = `${this.config.protocol}://${cleanHost}:${this.config.port || 80}`;
        const response = await fetch(`${baseUrl}/web/webclient/version_info`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {},
            }),
        });
        const result = await response.json();
        return this.parseVersionInfo(result.result);
    }
    async detectVersionViaHttp() {
        const cleanHost = this.config.host.replace(/^https?:\/\//, '');
        const baseUrl = `${this.config.protocol}://${cleanHost}:${this.config.port || 80}`;
        const response = await fetch(`${baseUrl}/web/webclient/version_info`);
        const result = await response.json();
        return this.parseVersionInfo(result);
    }
    parseVersionInfo(versionData) {
        const version = versionData.server_version || versionData.version;
        const cleanVersion = version.replace(/[^0-9.]/g, '');
        const versionParts = cleanVersion.split('.');
        const major = parseInt(versionParts[0]) || 0;
        const minor = parseInt(versionParts[1]) || 0;
        const patch = parseInt(versionParts[2]) || 0;
        return {
            major,
            minor,
            patch,
            series: `${major}.${minor}`,
            edition: this.detectEdition(versionData),
            serverVersion: version,
            protocolVersion: versionData.protocol_version || 1,
        };
    }
    detectEdition(versionData) {
        const version = versionData.server_version || versionData.version || '';
        return version.includes('e') || versionData.enterprise
            ? 'enterprise'
            : 'community';
    }
    createVersionAdapter(version) {
        if (version.major >= 18)
            return this.odooV18Adapter;
        if (version.major >= 17)
            return this.odooV17Adapter;
        if (version.major >= 15)
            return this.odooV15Adapter;
        if (version.major >= 13)
            return this.odooV13Adapter;
        throw new Error(`Unsupported Odoo version: ${version.series}`);
    }
    selectOptimalProtocol() {
        if (this.capabilities?.hasJsonRpc) {
            return this.jsonRpcProtocol;
        }
        return this.xmlRpcProtocol;
    }
    selectBestAuthMethod() {
        const supported = this.capabilities?.supportedAuthMethods || [];
        if (supported.includes(odoo_connection_config_1.AuthMethod.API_KEY))
            return odoo_connection_config_1.AuthMethod.API_KEY;
        if (supported.includes(odoo_connection_config_1.AuthMethod.OAUTH2))
            return odoo_connection_config_1.AuthMethod.OAUTH2;
        if (supported.includes(odoo_connection_config_1.AuthMethod.TOKEN))
            return odoo_connection_config_1.AuthMethod.TOKEN;
        return odoo_connection_config_1.AuthMethod.PASSWORD;
    }
};
exports.UniversalOdooAdapter = UniversalOdooAdapter;
exports.UniversalOdooAdapter = UniversalOdooAdapter = UniversalOdooAdapter_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [xmlrpc_protocol_1.XmlRpcProtocol,
        jsonrpc_protocol_1.JsonRpcProtocol,
        rest_protocol_1.RestApiProtocol,
        odoo_v18_adapter_1.OdooV18Adapter,
        odoo_v17_adapter_1.OdooV17Adapter,
        odoo_v15_adapter_1.OdooV15Adapter,
        odoo_v13_adapter_1.OdooV13Adapter])
], UniversalOdooAdapter);
//# sourceMappingURL=universal-odoo-adapter.js.map