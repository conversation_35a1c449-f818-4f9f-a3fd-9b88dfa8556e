{"version": 3, "file": "user-context.service.js", "sourceRoot": "", "sources": ["../../../../../src/infrastructure/adapters/odoo/user-context.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAmE;AACnE,uCAAuC;AAqBhC,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAIiB;IAH7B,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IACtD,WAAW,GAAuB,IAAI,CAAC;IAE/C,YAA8C,OAAwB;QAAxB,YAAO,GAAP,OAAO,CAAiB;IAAG,CAAC;IAK1E,cAAc,CAAC,UAAgC;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,GAAG;YACjB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACrD,UAAU;SACX,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC;IAKD,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAKD,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC;IACnC,CAAC;IAKD,SAAS;QACP,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACtC,OAAO,OAAO,CAAC,MAAM,CAAC;IACxB,CAAC;IAKD,YAAY;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACtC,OAAO,OAAO,CAAC,SAAS,CAAC;IAC3B,CAAC;IAKD,gBAAgB,CAAC,UAAgC;QAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;IAChF,CAAC;IAMO,sBAAsB;QAE5B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACxB,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ;gBACpC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK;gBAC9B,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;aAClC,CAAC;QACJ,CAAC;QAGD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAK,IAAI,CAAC,OAAO,CAAC,OAAe,CAAC,IAAI,EAAE,CAAC;YAC/D,MAAM,WAAW,GAAI,IAAI,CAAC,OAAO,CAAC,OAAe,CAAC,IAAI,CAAC;YACvD,OAAO;gBACL,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS;aAC5D,CAAC;QACJ,CAAC;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;QACtD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC;gBAEH,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtC,OAAO;oBACL,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;oBACjC,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE;iBACzD,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAGD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAW,CAAC;QAE9D,IAAI,MAAM,IAAI,QAAQ,EAAE,CAAC;YACvB,OAAO;gBACL,EAAE,EAAE,MAAM;gBACV,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;aACpC,CAAC;QACJ,CAAC;QAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,WAAW,EAAE,CAAC,CAAC;YACtF,OAAO;gBACL,EAAE,EAAE,WAAW;gBACf,QAAQ,EAAE,WAAW;gBACrB,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;aACpC,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IAKO,SAAS,CAAC,KAAa;QAC7B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAKO,iBAAiB;QACvB,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACzE,CAAC;IAKO,uBAAuB;QAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,IAAI,SAAS,CAAC;QACjF,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;QAGlE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/E,OAAO,QAAQ,IAAI,EAAE,CAAC;IACxB,CAAC;IAKD,kBAAkB;QAChB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YACnB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;YAC7C,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC3B,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;CACF,CAAA;AApLY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,EAAC,EAAE,KAAK,EAAE,cAAK,CAAC,OAAO,EAAE,CAAC;IAKtB,WAAA,IAAA,eAAM,EAAC,cAAO,CAAC,CAAA;;GAJjB,kBAAkB,CAoL9B"}