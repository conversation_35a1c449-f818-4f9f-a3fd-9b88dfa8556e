import { UniversalOdooAdapter } from './universal-odoo-adapter';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';
export declare class OdooSessionService {
    private readonly xmlRpcProtocol;
    private readonly jsonRpcProtocol;
    private readonly restApiProtocol;
    private readonly odooV18Adapter;
    private readonly odooV17Adapter;
    private readonly odooV15Adapter;
    private readonly odooV13Adapter;
    private readonly logger;
    private static adapter;
    private static isConnected;
    private static connectionConfig;
    constructor(xmlRpcProtocol: XmlRpcProtocol, jsonRpcProtocol: JsonRpcProtocol, restApiProtocol: RestApiProtocol, odooV18Adapter: OdooV18Adapter, odooV17Adapter: OdooV17Adapter, odooV15Adapter: OdooV15Adapter, odooV13Adapter: OdooV13Adapter);
    connect(config: OdooConnectionConfig): Promise<void>;
    disconnect(): Promise<void>;
    getAdapter(): UniversalOdooAdapter;
    isSessionActive(): boolean;
    getConnectionConfig(): OdooConnectionConfig | null;
    getVersionInfo(): any;
    getCapabilities(): any;
}
