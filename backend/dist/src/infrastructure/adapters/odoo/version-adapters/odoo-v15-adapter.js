"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooV15Adapter = void 0;
const common_1 = require("@nestjs/common");
const base_version_adapter_1 = require("./base-version-adapter");
const odoo_connection_config_1 = require("../../../../domain/value-objects/odoo-connection-config");
let OdooV15Adapter = class OdooV15Adapter extends base_version_adapter_1.BaseVersionAdapter {
    version = '15.0';
    capabilities = {
        hasJsonRpc: true,
        hasRestApi: false,
        hasGraphQL: false,
        hasWebSocket: false,
        hasTokenAuth: false,
        hasOAuth2: false,
        maxBatchSize: 600,
        supportedAuthMethods: [odoo_connection_config_1.AuthMethod.PASSWORD, odoo_connection_config_1.AuthMethod.API_KEY],
    };
    constructor() {
        super();
        this.initializeFieldMappings();
    }
    initializeFieldMappings() {
        this.setFieldMapping('res.partner', {});
        this.setFieldMapping('sale.order', {});
        this.setFieldMapping('account.move', {});
    }
    mapDomain(domain) {
        return domain;
    }
    handleResponse(response) {
        if (Array.isArray(response)) {
            return response.map((record) => this.processRecord(record));
        }
        return this.processRecord(response);
    }
    processRecord(record) {
        if (!record || typeof record !== 'object') {
            return record;
        }
        return record;
    }
};
exports.OdooV15Adapter = OdooV15Adapter;
exports.OdooV15Adapter = OdooV15Adapter = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], OdooV15Adapter);
//# sourceMappingURL=odoo-v15-adapter.js.map