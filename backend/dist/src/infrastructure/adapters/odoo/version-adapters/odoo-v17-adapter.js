"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooV17Adapter = void 0;
const common_1 = require("@nestjs/common");
const base_version_adapter_1 = require("./base-version-adapter");
const odoo_connection_config_1 = require("../../../../domain/value-objects/odoo-connection-config");
let OdooV17Adapter = class OdooV17Adapter extends base_version_adapter_1.BaseVersionAdapter {
    version = '17.0';
    capabilities = {
        hasJsonRpc: true,
        hasRestApi: false,
        hasGraphQL: false,
        hasWebSocket: false,
        hasTokenAuth: false,
        hasOAuth2: true,
        maxBatchSize: 800,
        supportedAuthMethods: [
            odoo_connection_config_1.AuthMethod.PASSWORD,
            odoo_connection_config_1.AuthMethod.API_KEY,
            odoo_connection_config_1.AuthMethod.OAUTH2,
        ],
    };
    constructor() {
        super();
        this.initializeFieldMappings();
    }
    initializeFieldMappings() {
        this.setFieldMapping('res.partner', {});
        this.setFieldMapping('sale.order', {});
        this.setFieldMapping('account.move', {});
    }
    mapDomain(domain) {
        return domain;
    }
    handleResponse(response) {
        if (Array.isArray(response)) {
            return response.map((record) => this.processRecord(record));
        }
        return this.processRecord(response);
    }
    processRecord(record) {
        if (!record || typeof record !== 'object') {
            return record;
        }
        return record;
    }
};
exports.OdooV17Adapter = OdooV17Adapter;
exports.OdooV17Adapter = OdooV17Adapter = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], OdooV17Adapter);
//# sourceMappingURL=odoo-v17-adapter.js.map