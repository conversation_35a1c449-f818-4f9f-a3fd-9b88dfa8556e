{"version": 3, "file": "base-version-adapter.js", "sourceRoot": "", "sources": ["../../../../../../src/infrastructure/adapters/odoo/version-adapters/base-version-adapter.ts"], "names": [], "mappings": ";;;AAMA,MAAsB,kBAAkB;IAI5B,aAAa,GAAkD,EAAE,CAAC;IAClE,cAAc,GAA2B;QACjD,WAAW,EAAE,aAAa;QAC1B,MAAM,EAAE,QAAQ;QAChB,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,QAAQ;KACjB,CAAC;IAEF,SAAS,CAAC,KAAa,EAAE,MAAgB;QACvC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QACtD,OAAO,MAAM;aACV,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC;aAC7C,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,SAAS,CAAC,MAAa;QAErB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,CAAC,KAAa,EAAE,MAAc;QACrC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;IAC/C,CAAC;IAED,cAAc,CAAC,QAAa;QAE1B,OAAO,QAAQ,CAAC;IAClB,CAAC;IAES,eAAe,CACvB,KAAa,EACb,QAAuC;QAEvC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;IAC5E,CAAC;IAES,gBAAgB,CAAC,QAAgC;QACzD,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,EAAE,CAAC;IAChE,CAAC;CACF;AA3CD,gDA2CC"}