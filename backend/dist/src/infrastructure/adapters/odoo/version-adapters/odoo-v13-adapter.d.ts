import { BaseVersionAdapter } from './base-version-adapter';
import { OdooCapabilities } from '../../../../domain/repositories/odoo-adapter.interface';
export declare class OdooV13Adapter extends BaseVersionAdapter {
    readonly version = "13.0";
    readonly capabilities: OdooCapabilities;
    constructor();
    private initializeFieldMappings;
    mapDomain(domain: any[]): any[];
    handleResponse(response: any): any;
    private processV13Record;
}
