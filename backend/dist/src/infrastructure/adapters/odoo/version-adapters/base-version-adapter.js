"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseVersionAdapter = void 0;
class BaseVersionAdapter {
    fieldMappings = {};
    methodMappings = {
        'search_read': 'search_read',
        'create': 'create',
        'write': 'write',
        'unlink': 'unlink'
    };
    mapFields(model, fields) {
        const modelMappings = this.fieldMappings[model] || {};
        return fields
            .map(field => modelMappings[field] ?? field)
            .filter(field => field !== null);
    }
    mapDomain(domain) {
        return domain;
    }
    mapMethod(model, method) {
        return this.methodMappings[method] || method;
    }
    handleResponse(response) {
        return response;
    }
    setFieldMapping(model, mappings) {
        this.fieldMappings[model] = { ...this.fieldMappings[model], ...mappings };
    }
    setMethodMapping(mappings) {
        this.methodMappings = { ...this.methodMappings, ...mappings };
    }
}
exports.BaseVersionAdapter = BaseVersionAdapter;
//# sourceMappingURL=base-version-adapter.js.map