"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OdooConnectionPoolService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooConnectionPoolService = void 0;
const common_1 = require("@nestjs/common");
const lru_cache_1 = require("lru-cache");
const universal_odoo_adapter_1 = require("./universal-odoo-adapter");
const xmlrpc_protocol_1 = require("../protocols/xmlrpc/xmlrpc-protocol");
const jsonrpc_protocol_1 = require("../protocols/jsonrpc/jsonrpc-protocol");
const rest_protocol_1 = require("../protocols/rest/rest-protocol");
const odoo_v18_adapter_1 = require("./version-adapters/odoo-v18-adapter");
const odoo_v17_adapter_1 = require("./version-adapters/odoo-v17-adapter");
const odoo_v15_adapter_1 = require("./version-adapters/odoo-v15-adapter");
const odoo_v13_adapter_1 = require("./version-adapters/odoo-v13-adapter");
let OdooConnectionPoolService = OdooConnectionPoolService_1 = class OdooConnectionPoolService {
    xmlRpcProtocol;
    jsonRpcProtocol;
    restApiProtocol;
    odooV18Adapter;
    odooV17Adapter;
    odooV15Adapter;
    odooV13Adapter;
    logger = new common_1.Logger(OdooConnectionPoolService_1.name);
    connectionPool = new lru_cache_1.LRUCache({
        max: 100,
        ttl: 1000 * 60 * 30,
        dispose: async (cachedConnection, key) => {
            try {
                await cachedConnection.adapter.disconnect();
                this.logger.log(`Disposed connection for key: ${key}`);
            }
            catch (error) {
                this.logger.error(`Error disposing connection for key: ${key}`, error);
            }
        },
        updateAgeOnGet: true,
        updateAgeOnHas: false,
    });
    constructor(xmlRpcProtocol, jsonRpcProtocol, restApiProtocol, odooV18Adapter, odooV17Adapter, odooV15Adapter, odooV13Adapter) {
        this.xmlRpcProtocol = xmlRpcProtocol;
        this.jsonRpcProtocol = jsonRpcProtocol;
        this.restApiProtocol = restApiProtocol;
        this.odooV18Adapter = odooV18Adapter;
        this.odooV17Adapter = odooV17Adapter;
        this.odooV15Adapter = odooV15Adapter;
        this.odooV13Adapter = odooV13Adapter;
    }
    async getConnection(userContext) {
        const cacheKey = this.generateCacheKey(userContext);
        const cachedConnection = this.connectionPool.get(cacheKey);
        if (cachedConnection) {
            cachedConnection.lastUsed = new Date();
            this.logger.debug(`Reusing connection for user: ${userContext.userId}`);
            return cachedConnection.adapter;
        }
        this.logger.log(`Creating new connection for user: ${userContext.userId}`);
        const adapter = await this.createConnection(userContext.odooConfig);
        const newCachedConnection = {
            adapter,
            lastUsed: new Date(),
            connectionConfig: userContext.odooConfig,
        };
        this.connectionPool.set(cacheKey, newCachedConnection);
        this.logger.log(`Connection cached for user: ${userContext.userId}. Pool size: ${this.connectionPool.size}`);
        return adapter;
    }
    async removeConnection(userContext) {
        const cacheKey = this.generateCacheKey(userContext);
        const cachedConnection = this.connectionPool.get(cacheKey);
        if (cachedConnection) {
            try {
                await cachedConnection.adapter.disconnect();
                this.connectionPool.delete(cacheKey);
                this.logger.log(`Removed connection for user: ${userContext.userId}`);
            }
            catch (error) {
                this.logger.error(`Error removing connection for user: ${userContext.userId}`, error);
            }
        }
    }
    async removeUserConnections(userId) {
        const keysToRemove = [];
        for (const [key, cachedConnection] of this.connectionPool.entries()) {
            if (key.startsWith(`${userId}:`)) {
                keysToRemove.push(key);
            }
        }
        for (const key of keysToRemove) {
            const cachedConnection = this.connectionPool.get(key);
            if (cachedConnection) {
                try {
                    await cachedConnection.adapter.disconnect();
                    this.connectionPool.delete(key);
                }
                catch (error) {
                    this.logger.error(`Error removing connection for key: ${key}`, error);
                }
            }
        }
        this.logger.log(`Removed ${keysToRemove.length} connections for user: ${userId}`);
    }
    getPoolStats() {
        return {
            size: this.connectionPool.size,
            maxSize: this.connectionPool.max,
            connections: Array.from(this.connectionPool.entries()).map(([key, cached]) => ({
                key,
                lastUsed: cached.lastUsed,
                host: cached.connectionConfig.host,
                database: cached.connectionConfig.database,
            })),
        };
    }
    hasConnection(userContext) {
        const cacheKey = this.generateCacheKey(userContext);
        return this.connectionPool.has(cacheKey);
    }
    findUserConnection(userId) {
        for (const [key, cachedConnection] of this.connectionPool.entries()) {
            if (key.startsWith(`${userId}:`)) {
                return cachedConnection;
            }
        }
        return null;
    }
    generateCacheKey(userContext) {
        const { userId, odooConfig } = userContext;
        return `${userId}:${odooConfig.host}:${odooConfig.database}:${odooConfig.username}`;
    }
    async createConnection(config) {
        const adapter = new universal_odoo_adapter_1.UniversalOdooAdapter(this.xmlRpcProtocol, this.jsonRpcProtocol, this.restApiProtocol, this.odooV18Adapter, this.odooV17Adapter, this.odooV15Adapter, this.odooV13Adapter);
        try {
            adapter.setConnectionConfig(config);
            await adapter.connect();
            await adapter.authenticate();
            return adapter;
        }
        catch (error) {
            this.logger.error('Failed to create Odoo connection', error);
            throw error;
        }
    }
    async onModuleDestroy() {
        this.logger.log('Cleaning up all Odoo connections...');
        const disconnectPromises = [];
        for (const [key, cachedConnection] of this.connectionPool.entries()) {
            disconnectPromises.push(cachedConnection.adapter.disconnect().catch((error) => {
                this.logger.error(`Error disconnecting adapter for key: ${key}`, error);
            }));
        }
        await Promise.allSettled(disconnectPromises);
        this.connectionPool.clear();
        this.logger.log('All Odoo connections cleaned up');
    }
};
exports.OdooConnectionPoolService = OdooConnectionPoolService;
exports.OdooConnectionPoolService = OdooConnectionPoolService = OdooConnectionPoolService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [xmlrpc_protocol_1.XmlRpcProtocol,
        jsonrpc_protocol_1.JsonRpcProtocol,
        rest_protocol_1.RestApiProtocol,
        odoo_v18_adapter_1.OdooV18Adapter,
        odoo_v17_adapter_1.OdooV17Adapter,
        odoo_v15_adapter_1.OdooV15Adapter,
        odoo_v13_adapter_1.OdooV13Adapter])
], OdooConnectionPoolService);
//# sourceMappingURL=odoo-connection-pool.service.js.map