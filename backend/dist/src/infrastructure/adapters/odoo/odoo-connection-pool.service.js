"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OdooConnectionPoolService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooConnectionPoolService = void 0;
const common_1 = require("@nestjs/common");
const lru_cache_1 = require("lru-cache");
const universal_odoo_adapter_1 = require("./universal-odoo-adapter");
const xmlrpc_protocol_1 = require("../protocols/xmlrpc/xmlrpc-protocol");
const jsonrpc_protocol_1 = require("../protocols/jsonrpc/jsonrpc-protocol");
const rest_protocol_1 = require("../protocols/rest/rest-protocol");
const odoo_v18_adapter_1 = require("./version-adapters/odoo-v18-adapter");
const odoo_v17_adapter_1 = require("./version-adapters/odoo-v17-adapter");
const odoo_v15_adapter_1 = require("./version-adapters/odoo-v15-adapter");
const odoo_v13_adapter_1 = require("./version-adapters/odoo-v13-adapter");
let OdooConnectionPoolService = OdooConnectionPoolService_1 = class OdooConnectionPoolService {
    xmlRpcProtocol;
    jsonRpcProtocol;
    restApiProtocol;
    odooV18Adapter;
    odooV17Adapter;
    odooV15Adapter;
    odooV13Adapter;
    logger = new common_1.Logger(OdooConnectionPoolService_1.name);
    connectionPool = new lru_cache_1.LRUCache({
        max: 100,
        ttl: 1000 * 60 * 30,
        dispose: async (cachedConnection, key) => {
            try {
                await cachedConnection.adapter.disconnect();
                this.logger.log(`Disposed connection for key: ${key}`);
            }
            catch (error) {
                this.logger.error(`Error disposing connection for key: ${key}`, error);
            }
        },
        updateAgeOnGet: true,
        updateAgeOnHas: false,
    });
    constructor(xmlRpcProtocol, jsonRpcProtocol, restApiProtocol, odooV18Adapter, odooV17Adapter, odooV15Adapter, odooV13Adapter) {
        this.xmlRpcProtocol = xmlRpcProtocol;
        this.jsonRpcProtocol = jsonRpcProtocol;
        this.restApiProtocol = restApiProtocol;
        this.odooV18Adapter = odooV18Adapter;
        this.odooV17Adapter = odooV17Adapter;
        this.odooV15Adapter = odooV15Adapter;
        this.odooV13Adapter = odooV13Adapter;
    }
    async getConnection(userContext) {
        const cacheKey = this.generateCacheKey(userContext);
        const cachedConnection = this.connectionPool.get(cacheKey);
        if (cachedConnection) {
            this.logger.debug(`Found cached connection for user: ${userContext.userId}, validating...`);
            const isValid = await this.validateConnection(cachedConnection.adapter);
            if (isValid) {
                cachedConnection.lastUsed = new Date();
                this.logger.debug(`Reusing valid connection for user: ${userContext.userId}`);
                return cachedConnection.adapter;
            }
            else {
                this.logger.warn(`Invalid connection found for user: ${userContext.userId}, removing from pool`);
                this.connectionPool.delete(cacheKey);
            }
        }
        this.logger.log(`Creating new connection for user: ${userContext.userId}`);
        const adapter = await this.createConnectionWithRetry(userContext.odooConfig);
        const newCachedConnection = {
            adapter,
            lastUsed: new Date(),
            connectionConfig: userContext.odooConfig,
        };
        this.connectionPool.set(cacheKey, newCachedConnection);
        this.logger.log(`Connection cached for user: ${userContext.userId}. Pool size: ${this.connectionPool.size}`);
        return adapter;
    }
    async removeConnection(userContext) {
        const cacheKey = this.generateCacheKey(userContext);
        const cachedConnection = this.connectionPool.get(cacheKey);
        if (cachedConnection) {
            try {
                await cachedConnection.adapter.disconnect();
                this.connectionPool.delete(cacheKey);
                this.logger.log(`Removed connection for user: ${userContext.userId}`);
            }
            catch (error) {
                this.logger.error(`Error removing connection for user: ${userContext.userId}`, error);
            }
        }
    }
    async removeUserConnections(userId) {
        const keysToRemove = [];
        for (const [key, cachedConnection] of this.connectionPool.entries()) {
            if (key.startsWith(`${userId}:`)) {
                keysToRemove.push(key);
            }
        }
        for (const key of keysToRemove) {
            const cachedConnection = this.connectionPool.get(key);
            if (cachedConnection) {
                try {
                    await cachedConnection.adapter.disconnect();
                    this.connectionPool.delete(key);
                }
                catch (error) {
                    this.logger.error(`Error removing connection for key: ${key}`, error);
                }
            }
        }
        this.logger.log(`Removed ${keysToRemove.length} connections for user: ${userId}`);
    }
    getPoolStats() {
        return {
            size: this.connectionPool.size,
            maxSize: this.connectionPool.max,
            connections: Array.from(this.connectionPool.entries()).map(([key, cached]) => ({
                key,
                lastUsed: cached.lastUsed,
                host: cached.connectionConfig.host,
                database: cached.connectionConfig.database,
            })),
        };
    }
    async warmUpConnections(configs) {
        this.logger.log(`Warming up ${configs.length} connections...`);
        const warmupPromises = configs.map(async ({ userId, config }) => {
            try {
                const userContext = {
                    userId,
                    sessionId: `warmup_${Date.now()}`,
                    odooConfig: config,
                };
                await this.getConnection(userContext);
                this.logger.debug(`Warmed up connection for user: ${userId}`);
            }
            catch (error) {
                this.logger.warn(`Failed to warm up connection for user: ${userId}`, error);
            }
        });
        await Promise.allSettled(warmupPromises);
        this.logger.log(`Connection warmup completed. Pool size: ${this.connectionPool.size}`);
    }
    async executeBatchOperations(operations) {
        this.logger.log(`Executing ${operations.length} batch operations...`);
        const results = await Promise.allSettled(operations.map(async ({ userContext, operation }) => {
            try {
                const adapter = await this.getConnection(userContext);
                const result = await operation(adapter);
                return { success: true, result, userId: userContext.userId };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    userId: userContext.userId
                };
            }
        }));
        return results.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            }
            else {
                return {
                    success: false,
                    error: result.reason?.message || 'Unknown error',
                    userId: operations[index].userContext.userId,
                };
            }
        });
    }
    async healthCheck() {
        const connections = Array.from(this.connectionPool.entries());
        const healthChecks = await Promise.allSettled(connections.map(async ([key, cached]) => {
            const isHealthy = await this.validateConnection(cached.adapter);
            return { key, healthy: isHealthy, lastUsed: cached.lastUsed };
        }));
        const details = healthChecks.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            }
            else {
                return {
                    key: connections[index][0],
                    healthy: false,
                    lastUsed: connections[index][1].lastUsed,
                };
            }
        });
        const healthyCount = details.filter(d => d.healthy).length;
        return {
            totalConnections: connections.length,
            healthyConnections: healthyCount,
            unhealthyConnections: connections.length - healthyCount,
            details,
        };
    }
    hasConnection(userContext) {
        const cacheKey = this.generateCacheKey(userContext);
        return this.connectionPool.has(cacheKey);
    }
    findUserConnection(userId) {
        for (const [key, cachedConnection] of this.connectionPool.entries()) {
            if (key.startsWith(`${userId}:`)) {
                return cachedConnection;
            }
        }
        return null;
    }
    generateCacheKey(userContext) {
        const { userId, odooConfig } = userContext;
        return `${userId}:${odooConfig.host}:${odooConfig.database}:${odooConfig.username}`;
    }
    async validateConnection(adapter) {
        try {
            const versionInfo = adapter.getVersionInfo();
            if (!versionInfo) {
                this.logger.debug('Connection validation failed: no version info');
                return false;
            }
            const capabilities = adapter.getCapabilities();
            if (!capabilities) {
                this.logger.debug('Connection validation failed: no capabilities');
                return false;
            }
            try {
                await adapter.searchRead('res.users', [['id', '=', 1]], { limit: 1 });
                return true;
            }
            catch (authError) {
                this.logger.debug('Connection validation failed: authentication test failed', authError.message);
                return false;
            }
        }
        catch (error) {
            this.logger.debug('Connection validation failed', error);
            return false;
        }
    }
    async createConnectionWithRetry(config, maxRetries = 3) {
        let lastError = new Error('No attempts made');
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const adapter = await this.createConnection(config);
                this.logger.log(`Connection created successfully on attempt ${attempt}`);
                return adapter;
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                this.logger.warn(`Connection attempt ${attempt} failed:`, lastError.message);
                if (attempt < maxRetries) {
                    const delay = Math.pow(2, attempt - 1) * 1000;
                    this.logger.log(`Retrying in ${delay}ms...`);
                    await this.sleep(delay);
                }
            }
        }
        this.logger.error(`Failed to create connection after ${maxRetries} attempts`);
        throw lastError;
    }
    async createConnection(config) {
        const adapter = new universal_odoo_adapter_1.UniversalOdooAdapter(this.xmlRpcProtocol, this.jsonRpcProtocol, this.restApiProtocol, this.odooV18Adapter, this.odooV17Adapter, this.odooV15Adapter, this.odooV13Adapter);
        try {
            adapter.setConnectionConfig(config);
            await adapter.connect();
            await adapter.authenticate();
            return adapter;
        }
        catch (error) {
            this.logger.error('Failed to create Odoo connection', error);
            throw error;
        }
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async cleanupStaleConnections(maxAgeMinutes = 60) {
        const cutoffTime = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
        const keysToRemove = [];
        for (const [key, cached] of this.connectionPool.entries()) {
            if (cached.lastUsed < cutoffTime) {
                keysToRemove.push(key);
            }
        }
        for (const key of keysToRemove) {
            const cached = this.connectionPool.get(key);
            if (cached) {
                try {
                    await cached.adapter.disconnect();
                    this.connectionPool.delete(key);
                }
                catch (error) {
                    this.logger.error(`Error cleaning up stale connection: ${key}`, error);
                }
            }
        }
        if (keysToRemove.length > 0) {
            this.logger.log(`Cleaned up ${keysToRemove.length} stale connections`);
        }
        return keysToRemove.length;
    }
    async refreshExpiringConnections(refreshThresholdMinutes = 25) {
        const refreshTime = new Date(Date.now() - refreshThresholdMinutes * 60 * 1000);
        let refreshedCount = 0;
        for (const [key, cached] of this.connectionPool.entries()) {
            if (cached.lastUsed < refreshTime) {
                try {
                    const isValid = await this.validateConnection(cached.adapter);
                    if (!isValid) {
                        const newAdapter = await this.createConnectionWithRetry(cached.connectionConfig);
                        cached.adapter = newAdapter;
                        cached.lastUsed = new Date();
                        refreshedCount++;
                        this.logger.debug(`Refreshed connection for key: ${key}`);
                    }
                }
                catch (error) {
                    this.logger.warn(`Failed to refresh connection: ${key}`, error);
                    this.connectionPool.delete(key);
                }
            }
        }
        if (refreshedCount > 0) {
            this.logger.log(`Refreshed ${refreshedCount} expiring connections`);
        }
        return refreshedCount;
    }
    getPoolMetrics() {
        const now = new Date();
        const connections = Array.from(this.connectionPool.entries());
        const ages = connections.map(([, cached]) => now.getTime() - cached.lastUsed.getTime());
        return {
            size: this.connectionPool.size,
            maxSize: this.connectionPool.max,
            utilizationPercent: (this.connectionPool.size / this.connectionPool.max) * 100,
            averageAgeMinutes: ages.length > 0 ? ages.reduce((a, b) => a + b, 0) / ages.length / 60000 : 0,
            oldestConnectionMinutes: ages.length > 0 ? Math.max(...ages) / 60000 : 0,
            newestConnectionMinutes: ages.length > 0 ? Math.min(...ages) / 60000 : 0,
        };
    }
    async onModuleDestroy() {
        this.logger.log('Cleaning up all Odoo connections...');
        const disconnectPromises = [];
        for (const [key, cachedConnection] of this.connectionPool.entries()) {
            disconnectPromises.push(cachedConnection.adapter.disconnect().catch((error) => {
                this.logger.error(`Error disconnecting adapter for key: ${key}`, error);
            }));
        }
        await Promise.allSettled(disconnectPromises);
        this.connectionPool.clear();
        this.logger.log('All Odoo connections cleaned up');
    }
};
exports.OdooConnectionPoolService = OdooConnectionPoolService;
exports.OdooConnectionPoolService = OdooConnectionPoolService = OdooConnectionPoolService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [xmlrpc_protocol_1.XmlRpcProtocol,
        jsonrpc_protocol_1.JsonRpcProtocol,
        rest_protocol_1.RestApiProtocol,
        odoo_v18_adapter_1.OdooV18Adapter,
        odoo_v17_adapter_1.OdooV17Adapter,
        odoo_v15_adapter_1.OdooV15Adapter,
        odoo_v13_adapter_1.OdooV13Adapter])
], OdooConnectionPoolService);
//# sourceMappingURL=odoo-connection-pool.service.js.map