{"version": 3, "file": "odoo-connection-pool.service.js", "sourceRoot": "", "sources": ["../../../../../src/infrastructure/adapters/odoo/odoo-connection-pool.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAqE;AACrE,yCAAqC;AACrC,qEAAgE;AAEhE,yEAAqE;AACrE,4EAAwE;AACxE,mEAAkE;AAClE,0EAAqE;AACrE,0EAAqE;AACrE,0EAAqE;AACrE,0EAAqE;AAe9D,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAmBjB;IACA;IACA;IACA;IACA;IACA;IACA;IAxBF,MAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;IAEpD,cAAc,GAAG,IAAI,oBAAQ,CAA2B;QACvE,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE;QACnB,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE;YACvC,IAAI,CAAC;gBACH,MAAM,gBAAgB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,GAAG,EAAE,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QACD,cAAc,EAAE,IAAI;QACpB,cAAc,EAAE,KAAK;KACtB,CAAC,CAAC;IAEH,YACmB,cAA8B,EAC9B,eAAgC,EAChC,eAAgC,EAChC,cAA8B,EAC9B,cAA8B,EAC9B,cAA8B,EAC9B,cAA8B;QAN9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,oBAAe,GAAf,eAAe,CAAiB;QAChC,oBAAe,GAAf,eAAe,CAAiB;QAChC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;IAC9C,CAAC;IAKJ,KAAK,CAAC,aAAa,CAAC,WAAwB;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAGpD,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,gBAAgB,EAAE,CAAC;YAErB,gBAAgB,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YACxE,OAAO,gBAAgB,CAAC,OAAO,CAAC;QAClC,CAAC;QAGD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAGpE,MAAM,mBAAmB,GAAqB;YAC5C,OAAO;YACP,QAAQ,EAAE,IAAI,IAAI,EAAE;YACpB,gBAAgB,EAAE,WAAW,CAAC,UAAU;SACzC,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;QAEvD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,WAAW,CAAC,MAAM,gBAAgB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7G,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,WAAwB;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACpD,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE3D,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,gBAAgB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC5C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YACxE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,MAAM,CAAC,GAAG,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACpE,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;YAC/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACtD,IAAI,gBAAgB,EAAE,CAAC;gBACrB,IAAI,CAAC;oBACH,MAAM,gBAAgB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;oBAC5C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,YAAY,CAAC,MAAM,0BAA0B,MAAM,EAAE,CAAC,CAAC;IACpF,CAAC;IAKD,YAAY;QACV,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;YAC9B,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG;YAChC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC7E,GAAG;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI;gBAClC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ;aAC3C,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAKD,aAAa,CAAC,WAAwB;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAKD,kBAAkB,CAAC,MAAc;QAC/B,KAAK,MAAM,CAAC,GAAG,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACpE,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,OAAO,gBAAgB,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,gBAAgB,CAAC,WAAwB;QAC/C,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC;QAE3C,OAAO,GAAG,MAAM,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;IACtF,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,MAA4B;QACzD,MAAM,OAAO,GAAG,IAAI,6CAAoB,CACtC,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,CACpB,CAAC;QAEF,IAAI,CAAC;YACH,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACpC,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,OAAO,CAAC,YAAY,EAAE,CAAC;YAC7B,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEvD,MAAM,kBAAkB,GAAoB,EAAE,CAAC;QAE/C,KAAK,MAAM,CAAC,GAAG,EAAE,gBAAgB,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACpE,kBAAkB,CAAC,IAAI,CACrB,gBAAgB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1E,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACrD,CAAC;CACF,CAAA;AApMY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAoBwB,gCAAc;QACb,kCAAe;QACf,+BAAe;QAChB,iCAAc;QACd,iCAAc;QACd,iCAAc;QACd,iCAAc;GAzBtC,yBAAyB,CAoMrC"}