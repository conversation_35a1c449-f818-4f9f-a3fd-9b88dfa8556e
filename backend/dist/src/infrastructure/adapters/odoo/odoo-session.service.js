"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OdooSessionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooSessionService = void 0;
const common_1 = require("@nestjs/common");
const universal_odoo_adapter_1 = require("./universal-odoo-adapter");
const xmlrpc_protocol_1 = require("../protocols/xmlrpc/xmlrpc-protocol");
const jsonrpc_protocol_1 = require("../protocols/jsonrpc/jsonrpc-protocol");
const rest_protocol_1 = require("../protocols/rest/rest-protocol");
const odoo_v18_adapter_1 = require("./version-adapters/odoo-v18-adapter");
const odoo_v17_adapter_1 = require("./version-adapters/odoo-v17-adapter");
const odoo_v15_adapter_1 = require("./version-adapters/odoo-v15-adapter");
const odoo_v13_adapter_1 = require("./version-adapters/odoo-v13-adapter");
let OdooSessionService = class OdooSessionService {
    static { OdooSessionService_1 = this; }
    xmlRpcProtocol;
    jsonRpcProtocol;
    restApiProtocol;
    odooV18Adapter;
    odooV17Adapter;
    odooV15Adapter;
    odooV13Adapter;
    logger = new common_1.Logger(OdooSessionService_1.name);
    static adapter = null;
    static isConnected = false;
    static connectionConfig = null;
    constructor(xmlRpcProtocol, jsonRpcProtocol, restApiProtocol, odooV18Adapter, odooV17Adapter, odooV15Adapter, odooV13Adapter) {
        this.xmlRpcProtocol = xmlRpcProtocol;
        this.jsonRpcProtocol = jsonRpcProtocol;
        this.restApiProtocol = restApiProtocol;
        this.odooV18Adapter = odooV18Adapter;
        this.odooV17Adapter = odooV17Adapter;
        this.odooV15Adapter = odooV15Adapter;
        this.odooV13Adapter = odooV13Adapter;
    }
    async connect(config) {
        try {
            if (OdooSessionService_1.isConnected && OdooSessionService_1.adapter) {
                await this.disconnect();
            }
            OdooSessionService_1.adapter = new universal_odoo_adapter_1.UniversalOdooAdapter(this.xmlRpcProtocol, this.jsonRpcProtocol, this.restApiProtocol, this.odooV18Adapter, this.odooV17Adapter, this.odooV15Adapter, this.odooV13Adapter);
            OdooSessionService_1.connectionConfig = config;
            OdooSessionService_1.adapter.setConnectionConfig(config);
            await OdooSessionService_1.adapter.connect();
            await OdooSessionService_1.adapter.authenticate();
            OdooSessionService_1.isConnected = true;
            this.logger.log(`Connected to Odoo at ${config.host}`);
        }
        catch (error) {
            OdooSessionService_1.isConnected = false;
            OdooSessionService_1.adapter = null;
            OdooSessionService_1.connectionConfig = null;
            this.logger.error('Failed to connect to Odoo', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.adapter && this.isConnected) {
            try {
                await this.adapter.disconnect();
                this.logger.log('Disconnected from Odoo');
            }
            catch (error) {
                this.logger.error('Error during disconnect', error);
            }
        }
        this.adapter = null;
        this.isConnected = false;
        this.connectionConfig = null;
    }
    getAdapter() {
        if (!this.isConnected || !this.adapter) {
            throw new Error('Not connected to Odoo. Call connect() first.');
        }
        return this.adapter;
    }
    isSessionActive() {
        return this.isConnected && this.adapter !== null;
    }
    getConnectionConfig() {
        return this.connectionConfig;
    }
    getVersionInfo() {
        if (!this.adapter) {
            return null;
        }
        return this.adapter.getVersionInfo();
    }
    getCapabilities() {
        if (!this.adapter) {
            return null;
        }
        return this.adapter.getCapabilities();
    }
};
exports.OdooSessionService = OdooSessionService;
exports.OdooSessionService = OdooSessionService = OdooSessionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [xmlrpc_protocol_1.XmlRpcProtocol,
        jsonrpc_protocol_1.JsonRpcProtocol,
        rest_protocol_1.RestApiProtocol,
        odoo_v18_adapter_1.OdooV18Adapter,
        odoo_v17_adapter_1.OdooV17Adapter,
        odoo_v15_adapter_1.OdooV15Adapter,
        odoo_v13_adapter_1.OdooV13Adapter])
], OdooSessionService);
//# sourceMappingURL=odoo-session.service.js.map