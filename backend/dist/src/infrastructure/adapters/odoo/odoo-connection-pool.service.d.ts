import { OnModuleD<PERSON>roy } from '@nestjs/common';
import { UniversalOdooAdapter } from './universal-odoo-adapter';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';
export interface UserContext {
    userId: string;
    sessionId: string;
    odooConfig: OdooConnectionConfig;
}
export interface CachedConnection {
    adapter: UniversalOdooAdapter;
    lastUsed: Date;
    connectionConfig: OdooConnectionConfig;
}
export declare class OdooConnectionPoolService implements OnModuleDestroy {
    private readonly xmlRpcProtocol;
    private readonly jsonRpcProtocol;
    private readonly restApiProtocol;
    private readonly odooV18Adapter;
    private readonly odooV17Adapter;
    private readonly odooV15Adapter;
    private readonly odooV13Adapter;
    private readonly logger;
    private readonly connectionPool;
    constructor(xmlRpcProtocol: XmlRpcProtocol, jsonRpcProtocol: JsonRpcProtocol, restApiProtocol: RestApiProtocol, odooV18Adapter: OdooV18Adapter, odooV17Adapter: OdooV17Adapter, odooV15Adapter: OdooV15Adapter, odooV13Adapter: OdooV13Adapter);
    getConnection(userContext: UserContext): Promise<UniversalOdooAdapter>;
    removeConnection(userContext: UserContext): Promise<void>;
    removeUserConnections(userId: string): Promise<void>;
    getPoolStats(): {
        size: number;
        maxSize: number;
        connections: {
            key: string;
            lastUsed: Date;
            host: string;
            database: string;
        }[];
    };
    hasConnection(userContext: UserContext): boolean;
    findUserConnection(userId: string): CachedConnection | null;
    private generateCacheKey;
    private createConnection;
    onModuleDestroy(): Promise<void>;
}
