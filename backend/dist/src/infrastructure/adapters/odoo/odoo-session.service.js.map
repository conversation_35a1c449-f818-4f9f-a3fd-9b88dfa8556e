{"version": 3, "file": "odoo-session.service.js", "sourceRoot": "", "sources": ["../../../../../src/infrastructure/adapters/odoo/odoo-session.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qEAAgE;AAEhE,yEAAqE;AACrE,4EAAwE;AACxE,mEAAkE;AAClE,0EAAqE;AACrE,0EAAqE;AACrE,0EAAqE;AACrE,0EAAqE;AAG9D,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;;IAOV;IACA;IACA;IACA;IACA;IACA;IACA;IAZF,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IACtD,MAAM,CAAC,OAAO,GAAgC,IAAI,CAAC;IACnD,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,MAAM,CAAC,gBAAgB,GAAgC,IAAI,CAAC;IAEpE,YACmB,cAA8B,EAC9B,eAAgC,EAChC,eAAgC,EAChC,cAA8B,EAC9B,cAA8B,EAC9B,cAA8B,EAC9B,cAA8B;QAN9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,oBAAe,GAAf,eAAe,CAAiB;QAChC,oBAAe,GAAf,eAAe,CAAiB;QAChC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;IAC9C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,MAA4B;QACxC,IAAI,CAAC;YAEH,IAAI,oBAAkB,CAAC,WAAW,IAAI,oBAAkB,CAAC,OAAO,EAAE,CAAC;gBACjE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1B,CAAC;YAGD,oBAAkB,CAAC,OAAO,GAAG,IAAI,6CAAoB,CACnD,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,CACpB,CAAC;YACF,oBAAkB,CAAC,gBAAgB,GAAG,MAAM,CAAC;YAG7C,oBAAkB,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,oBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC3C,MAAM,oBAAkB,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAEhD,oBAAkB,CAAC,WAAW,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,oBAAkB,CAAC,WAAW,GAAG,KAAK,CAAC;YACvC,oBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;YAClC,oBAAkB,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;IACnD,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IACvC,CAAC;IAED,eAAe;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;IACxC,CAAC;;AA7FU,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAQwB,gCAAc;QACb,kCAAe;QACf,+BAAe;QAChB,iCAAc;QACd,iCAAc;QACd,iCAAc;QACd,iCAAc;GAbtC,kBAAkB,CA8F9B"}