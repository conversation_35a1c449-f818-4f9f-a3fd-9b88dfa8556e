import { OdooConnectionUseCase } from '../../application/use-cases/odoo-connection.use-case';
import { OdooConnectionDto, SearchReadDto, CreateRecordDto, UpdateRecordDto, DeleteRecordDto } from '../../application/dtos/odoo-connection.dto';
export declare class OdooController {
    private readonly odooConnectionUseCase;
    constructor(odooConnectionUseCase: OdooConnectionUseCase);
    connect(connectionDto: OdooConnectionDto): Promise<{
        success: boolean;
        message: string;
        version: import("../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null;
        capabilities: import("../../domain/value-objects/odoo-connection-config").OdooCapabilities | null;
        deprecationNotice: {
            message: string;
            newEndpoint: string;
            deprecatedSince: string;
        };
    }>;
    getVersion(): Promise<import("../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null>;
    getCapabilities(): Promise<import("../../domain/value-objects/odoo-connection-config").OdooCapabilities | null>;
    searchRead(model: string, searchDto: SearchReadDto): Promise<any[]>;
    create(model: string, createDto: CreateRecordDto): Promise<{
        success: boolean;
        id: number;
        message: string;
    }>;
    update(model: string, updateDto: UpdateRecordDto): Promise<{
        success: boolean;
        message: string;
    }>;
    delete(model: string, deleteDto: DeleteRecordDto): Promise<{
        success: boolean;
        message: string;
    }>;
    execute(model: string, method: string, body: {
        args?: any[];
        kwargs?: any;
    }): Promise<{
        success: boolean;
        result: any;
        message: string;
    }>;
    disconnect(): Promise<{
        success: boolean;
        message: string;
    }>;
}
