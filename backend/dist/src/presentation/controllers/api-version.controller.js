"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiVersionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
let ApiVersionController = class ApiVersionController {
    getApiInfo() {
        return {
            name: 'Universal Odoo Adapter API',
            description: 'A comprehensive API for connecting to multiple Odoo versions with automatic protocol selection',
            currentVersion: 'v1',
            availableVersions: ['v1'],
            endpoints: {
                v1: '/api/v1',
            },
            documentation: {
                swagger: '/api/docs',
                v1: '/api/v1/docs',
            },
            features: {
                v1: [
                    'Multi-version Odoo support (13, 15, 17, 18+)',
                    'Multi-protocol support (XML-RPC, JSON-RPC, REST)',
                    'Automatic version detection',
                    'Intelligent protocol selection',
                    'Field mapping between versions',
                    'CRUD operations',
                    'Custom method execution',
                ],
            },
            compatibility: {
                odooVersions: ['13.0', '15.0', '17.0', '18.0+'],
                protocols: ['XML-RPC', 'JSON-RPC', 'REST API'],
                authMethods: ['Password', 'API Key', 'OAuth2', 'Token'],
            },
        };
    }
    getHealth() {
        return {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: 'v1',
            uptime: process.uptime(),
        };
    }
};
exports.ApiVersionController = ApiVersionController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get API information and available versions' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'API information retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', example: 'Universal Odoo Adapter API' },
                description: { type: 'string' },
                currentVersion: { type: 'string', example: 'v1' },
                availableVersions: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['v1'],
                },
                endpoints: {
                    type: 'object',
                    properties: {
                        v1: { type: 'string', example: '/api/v1' },
                    },
                },
                documentation: {
                    type: 'object',
                    properties: {
                        swagger: { type: 'string', example: '/api/docs' },
                        v1: { type: 'string', example: '/api/v1/docs' },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getApiInfo", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Health check endpoint' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'API is healthy' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getHealth", null);
exports.ApiVersionController = ApiVersionController = __decorate([
    (0, swagger_1.ApiTags)('API Information'),
    (0, common_1.Controller)('api')
], ApiVersionController);
//# sourceMappingURL=api-version.controller.js.map