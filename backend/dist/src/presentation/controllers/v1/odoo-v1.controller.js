"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooV1Controller = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const odoo_connection_use_case_1 = require("../../../application/use-cases/odoo-connection.use-case");
const odoo_connection_dto_1 = require("../../../application/dtos/odoo-connection.dto");
let OdooV1Controller = class OdooV1Controller {
    odooConnectionUseCase;
    constructor(odooConnectionUseCase) {
        this.odooConnectionUseCase = odooConnectionUseCase;
    }
    async connect(connectionDto) {
        await this.odooConnectionUseCase.connect(connectionDto);
        return {
            success: true,
            message: 'Connected to Odoo successfully',
            version: await this.odooConnectionUseCase.getVersionInfo(),
            capabilities: await this.odooConnectionUseCase.getCapabilities(),
            apiVersion: 'v1',
        };
    }
    async getVersion() {
        return await this.odooConnectionUseCase.getVersionInfo();
    }
    async getCapabilities() {
        return await this.odooConnectionUseCase.getCapabilities();
    }
    async searchRead(model, searchDto) {
        const { domain, fields, limit, offset, order } = searchDto;
        return await this.odooConnectionUseCase.searchRead(model, domain, {
            fields,
            limit,
            offset,
            order,
        });
    }
    async create(model, createDto) {
        const recordId = await this.odooConnectionUseCase.create(model, createDto.values);
        return {
            success: true,
            id: recordId,
            message: 'Record created successfully',
            apiVersion: 'v1',
        };
    }
    async update(model, updateDto) {
        const success = await this.odooConnectionUseCase.update(model, updateDto.ids, updateDto.values);
        return {
            success,
            message: success ? 'Records updated successfully' : 'Update failed',
            apiVersion: 'v1',
        };
    }
    async delete(model, deleteDto) {
        const success = await this.odooConnectionUseCase.delete(model, deleteDto.ids);
        return {
            success,
            message: success ? 'Records deleted successfully' : 'Delete failed',
            apiVersion: 'v1',
        };
    }
    async execute(model, method, body) {
        const { args = [], kwargs = {} } = body;
        const result = await this.odooConnectionUseCase.execute(model, method, args, kwargs);
        return {
            success: true,
            result,
            message: 'Method executed successfully',
            apiVersion: 'v1',
        };
    }
    async disconnect() {
        await this.odooConnectionUseCase.disconnect();
        return {
            success: true,
            message: 'Disconnected from Odoo successfully',
            apiVersion: 'v1',
        };
    }
};
exports.OdooV1Controller = OdooV1Controller;
__decorate([
    (0, common_1.Post)('connect'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Connect to Odoo instance',
        description: 'Establishes connection to an Odoo instance with automatic version detection and protocol selection',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Successfully connected to Odoo',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Connected to Odoo successfully' },
                version: {
                    type: 'object',
                    properties: {
                        major: { type: 'number', example: 18 },
                        minor: { type: 'number', example: 0 },
                        series: { type: 'string', example: '18.0' },
                        edition: { type: 'string', example: 'enterprise' },
                    },
                },
                capabilities: {
                    type: 'object',
                    properties: {
                        hasJsonRpc: { type: 'boolean', example: true },
                        hasRestApi: { type: 'boolean', example: true },
                        maxBatchSize: { type: 'number', example: 1000 },
                        supportedAuthMethods: {
                            type: 'array',
                            items: { type: 'string' },
                            example: ['password', 'api_key', 'oauth2'],
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid connection parameters' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Connection failed' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [odoo_connection_dto_1.OdooConnectionDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "connect", null);
__decorate([
    (0, common_1.Get)('version'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Odoo version information',
        description: 'Returns detailed version information of the connected Odoo instance',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Version information retrieved',
        schema: {
            type: 'object',
            properties: {
                major: { type: 'number', example: 18 },
                minor: { type: 'number', example: 0 },
                patch: { type: 'number', example: 0 },
                series: { type: 'string', example: '18.0' },
                edition: { type: 'string', example: 'enterprise' },
                serverVersion: { type: 'string', example: '********.3' },
                protocolVersion: { type: 'number', example: 1 },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getVersion", null);
__decorate([
    (0, common_1.Get)('capabilities'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Odoo capabilities',
        description: 'Returns the capabilities and features available in the connected Odoo instance',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Capabilities retrieved',
        schema: {
            type: 'object',
            properties: {
                hasJsonRpc: { type: 'boolean', example: true },
                hasRestApi: { type: 'boolean', example: true },
                hasGraphQL: { type: 'boolean', example: false },
                hasWebSocket: { type: 'boolean', example: true },
                hasTokenAuth: { type: 'boolean', example: true },
                hasOAuth2: { type: 'boolean', example: true },
                maxBatchSize: { type: 'number', example: 1000 },
                supportedAuthMethods: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['password', 'api_key', 'oauth2', 'token'],
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getCapabilities", null);
__decorate([
    (0, common_1.Post)(':model/search'),
    (0, swagger_1.ApiOperation)({
        summary: 'Search and read records from Odoo model',
        description: 'Performs search_read operation on the specified Odoo model with domain filtering and field selection',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name (e.g., res.partner, sale.order, product.product)',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records retrieved successfully',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    name: { type: 'string', example: 'Partner Name' },
                    email: { type: 'string', example: '<EMAIL>' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.SearchReadDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "searchRead", null);
__decorate([
    (0, common_1.Post)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new record in Odoo model',
        description: 'Creates a new record in the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Record created successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                id: { type: 'number', example: 123 },
                message: { type: 'string', example: 'Record created successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.CreateRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "create", null);
__decorate([
    (0, common_1.Put)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update records in Odoo model',
        description: 'Updates existing records in the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records updated successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Records updated successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.UpdateRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete records from Odoo model',
        description: 'Deletes records from the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records deleted successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Records deleted successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.DeleteRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "delete", null);
__decorate([
    (0, common_1.Post)(':model/execute/:method'),
    (0, swagger_1.ApiOperation)({
        summary: 'Execute custom method on Odoo model',
        description: 'Executes a custom method on the specified Odoo model with arguments and keyword arguments',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiParam)({
        name: 'method',
        description: 'Method name to execute',
        example: 'search_count',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Method executed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                result: { description: 'Method execution result' },
                message: { type: 'string', example: 'Method executed successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Param)('method')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "execute", null);
__decorate([
    (0, common_1.Post)('disconnect'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Disconnect from Odoo instance',
        description: 'Closes the connection to the Odoo instance and cleans up resources',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Disconnected successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Disconnected from Odoo successfully',
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "disconnect", null);
exports.OdooV1Controller = OdooV1Controller = __decorate([
    (0, swagger_1.ApiTags)('Odoo Integration v1'),
    (0, common_1.Controller)({ path: 'odoo', version: '1' }),
    __metadata("design:paramtypes", [odoo_connection_use_case_1.OdooConnectionUseCase])
], OdooV1Controller);
//# sourceMappingURL=odoo-v1.controller.js.map