import { OdooConnectionUseCase } from '../../../application/use-cases/odoo-connection.use-case';
import { OdooConnectionDto, SearchReadDto, CreateRecordDto, UpdateRecordDto, DeleteRecordDto } from '../../../application/dtos/odoo-connection.dto';
export declare class OdooV1Controller {
    private readonly odooConnectionUseCase;
    constructor(odooConnectionUseCase: OdooConnectionUseCase);
    connect(connectionDto: OdooConnectionDto): Promise<{
        success: boolean;
        message: string;
        version: any;
        capabilities: any;
        apiVersion: string;
    }>;
    getVersion(): Promise<any>;
    getCapabilities(): Promise<any>;
    searchRead(model: string, searchDto: SearchReadDto): Promise<any[]>;
    create(model: string, createDto: CreateRecordDto): Promise<{
        success: boolean;
        id: number;
        message: string;
        apiVersion: string;
    }>;
    update(model: string, updateDto: UpdateRecordDto): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    delete(model: string, deleteDto: DeleteRecordDto): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    execute(model: string, method: string, body: {
        args?: any[];
        kwargs?: any;
    }): Promise<{
        success: boolean;
        result: any;
        message: string;
        apiVersion: string;
    }>;
    disconnect(): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
}
