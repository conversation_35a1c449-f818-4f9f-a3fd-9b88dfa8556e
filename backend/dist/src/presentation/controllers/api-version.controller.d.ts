export declare class ApiVersionController {
    getApiInfo(): {
        name: string;
        description: string;
        currentVersion: string;
        availableVersions: string[];
        endpoints: {
            v1: string;
        };
        documentation: {
            swagger: string;
            v1: string;
        };
        features: {
            v1: string[];
        };
        compatibility: {
            odooVersions: string[];
            protocols: string[];
            authMethods: string[];
        };
    };
    getHealth(): {
        status: string;
        timestamp: string;
        version: string;
        uptime: number;
    };
}
