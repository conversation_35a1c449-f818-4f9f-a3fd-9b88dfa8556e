import { OdooConnectionUseCase } from '../../application/use-cases/odoo-connection.use-case';
export declare class ApiVersionController {
    private readonly odooConnectionUseCase;
    constructor(odooConnectionUseCase: OdooConnectionUseCase);
    getApiInfo(): {
        name: string;
        description: string;
        currentVersion: string;
        availableVersions: string[];
        endpoints: {
            v1: string;
        };
        documentation: {
            swagger: string;
            v1: string;
        };
        features: {
            v1: string[];
        };
        compatibility: {
            odooVersions: string[];
            protocols: string[];
            authMethods: string[];
        };
    };
    getHealth(): {
        status: string;
        timestamp: string;
        version: string;
        uptime: number;
    };
    getPoolStats(): {
        size: number;
        maxSize: number;
        connections: {
            key: string;
            lastUsed: Date;
            host: string;
            database: string;
        }[];
    };
    getPoolMetrics(): {
        size: number;
        maxSize: number;
        utilizationPercent: number;
        averageAgeMinutes: number;
        oldestConnectionMinutes: number;
        newestConnectionMinutes: number;
    };
    getPoolHealth(): Promise<{
        totalConnections: number;
        healthyConnections: number;
        unhealthyConnections: number;
        details: Array<{
            key: string;
            healthy: boolean;
            lastUsed: Date;
        }>;
    }>;
}
