"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeprecationMiddleware = void 0;
const common_1 = require("@nestjs/common");
let DeprecationMiddleware = class DeprecationMiddleware {
    use(req, res, next) {
        if (req.path.startsWith('/api/odoo') && !req.path.startsWith('/api/v')) {
            res.setHeader('Deprecation', 'true');
            res.setHeader('Sunset', '2025-12-31');
            res.setHeader('Link', '</api/v1/odoo>; rel="successor-version"');
            res.setHeader('Warning', '299 - "This API version is deprecated. Please migrate to /api/v1/odoo"');
            res.setHeader('X-API-Deprecation-Info', JSON.stringify({
                deprecated: true,
                deprecatedSince: '2025-01-26',
                sunsetDate: '2025-12-31',
                newEndpoint: req.path.replace('/api/odoo', '/api/v1/odoo'),
                migrationGuide: '/api/docs#migration',
            }));
        }
        next();
    }
};
exports.DeprecationMiddleware = DeprecationMiddleware;
exports.DeprecationMiddleware = DeprecationMiddleware = __decorate([
    (0, common_1.Injectable)()
], DeprecationMiddleware);
//# sourceMappingURL=deprecation.middleware.js.map