export interface OdooBaseModel {
    id: number;
    create_date?: string;
    write_date?: string;
    create_uid?: number;
    write_uid?: number;
}
export interface Partner extends OdooBaseModel {
    name: string;
    email?: string;
    phone?: string;
    is_company: boolean;
    parent_id?: number;
    child_ids: number[];
    category_id: number[];
    country_id?: number;
    state_id?: number;
    city?: string;
    street?: string;
    zip?: string;
    website?: string;
    vat?: string;
}
export interface SaleOrder extends OdooBaseModel {
    name: string;
    partner_id: number;
    date_order: string;
    state: 'draft' | 'sent' | 'sale' | 'done' | 'cancel';
    amount_total: number;
    amount_untaxed: number;
    amount_tax: number;
    currency_id: number;
    order_line: number[];
    invoice_ids: number[];
}
export interface Product extends OdooBaseModel {
    name: string;
    default_code?: string;
    barcode?: string;
    list_price: number;
    standard_price: number;
    type: 'consu' | 'service' | 'product';
    categ_id: number;
    uom_id: number;
    uom_po_id: number;
    active: boolean;
    sale_ok: boolean;
    purchase_ok: boolean;
}
export interface Invoice extends OdooBaseModel {
    name: string;
    partner_id: number;
    invoice_date: string;
    invoice_date_due?: string;
    state: 'draft' | 'posted' | 'cancel';
    move_type: 'out_invoice' | 'in_invoice' | 'out_refund' | 'in_refund';
    amount_total: number;
    amount_untaxed: number;
    amount_tax: number;
    currency_id: number;
    invoice_line_ids: number[];
}
