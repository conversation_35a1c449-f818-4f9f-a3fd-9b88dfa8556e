/**
 * Universal Odoo Adapter Usage Examples
 *
 * This file demonstrates how to use the Universal Odoo Adapter
 * to connect to different Odoo versions and perform CRUD operations.
 *
 * NOTE: This file is commented out to avoid compilation errors during development.
 * Uncomment and update the constructor calls when using in a real application.
 */

// import { UniversalOdooAdapter } from '../src/infrastructure/adapters/odoo/universal-odoo-adapter';
// import { OdooConnectionConfig } from '../src/domain/value-objects/odoo-connection-config';
// import { Partner, SaleOrder, Product } from '../src/domain/entities/odoo-record';

/*
async function exampleUsage() {
  // Initialize the adapter (in real app, this would be injected)
  // const adapter = new UniversalOdooAdapter(
  //   null, // These would be injected in real app
  //   null,
  //   null,
  //   null,
  //   null,
  //   null,
  //   null
  // );

  // Configuration for your Odoo instance
  const config: OdooConnectionConfig = {
    host: 'your-odoo-instance.com',
    database: 'your-database',
    username: 'admin',
    password: 'admin',
    protocol: 'https',
    port: 443
  };

  try {
    // 1. Connect to Odoo (automatically detects version and selects optimal protocol)
    // adapter.setConnectionConfig(config);
    // await adapter.connect();
    // await adapter.authenticate();

    console.log('✅ Connected to Odoo successfully!');
    // console.log('Version:', adapter.getVersionInfo());
    // console.log('Capabilities:', adapter.getCapabilities());

    // 2. Search and read partners
    console.log('\n📋 Searching for partners...');
    const partners = await adapter.searchRead<Partner>(
      'res.partner',
      [['is_company', '=', true]], // Domain filter
      {
        fields: ['name', 'email', 'phone', 'website'],
        limit: 10,
        order: 'name ASC'
      }
    );
    console.log(`Found ${partners.length} company partners:`, partners);

    // 3. Create a new partner
    console.log('\n➕ Creating a new partner...');
    const newPartnerId = await adapter.create<Partner>('res.partner', {
      name: 'Test Company Ltd.',
      is_company: true,
      email: '<EMAIL>',
      phone: '+1234567890',
      website: 'https://testcompany.com'
    });
    console.log(`Created partner with ID: ${newPartnerId}`);

    // 4. Update the partner
    console.log('\n✏️ Updating partner...');
    const updateSuccess = await adapter.write<Partner>('res.partner', [newPartnerId], {
      phone: '+0987654321',
      city: 'New York'
    });
    console.log(`Update ${updateSuccess ? 'successful' : 'failed'}`);

    // 5. Search for products
    console.log('\n🛍️ Searching for products...');
    const products = await adapter.searchRead<Product>(
      'product.product',
      [['sale_ok', '=', true]],
      {
        fields: ['name', 'default_code', 'list_price', 'type'],
        limit: 5
      }
    );
    console.log(`Found ${products.length} saleable products:`, products);

    // 6. Execute custom method
    console.log('\n🔧 Executing custom method...');
    const result = await adapter.execute(
      'res.partner',
      'search_count',
      [[['is_company', '=', true]]]
    );
    console.log(`Total number of companies: ${result}`);

    // 7. Search for sale orders
    console.log('\n📊 Searching for sale orders...');
    const saleOrders = await adapter.searchRead<SaleOrder>(
      'sale.order',
      [['state', 'in', ['sale', 'done']]],
      {
        fields: ['name', 'partner_id', 'date_order', 'amount_total', 'state'],
        limit: 5,
        order: 'date_order DESC'
      }
    );
    console.log(`Found ${saleOrders.length} confirmed sale orders:`, saleOrders);

    // 8. Clean up - delete the test partner
    console.log('\n🗑️ Cleaning up...');
    const deleteSuccess = await adapter.unlink('res.partner', [newPartnerId]);
    console.log(`Delete ${deleteSuccess ? 'successful' : 'failed'}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    // Always disconnect
    await adapter.disconnect();
    console.log('\n👋 Disconnected from Odoo');
  }
}

}

// Example of working with different Odoo versions
async function multiVersionExample() {
  const configs = [
    {
      name: 'Odoo 18 Enterprise',
      config: {
        host: 'odoo18.example.com',
        database: 'odoo18_db',
        username: 'admin',
        password: 'admin',
        protocol: 'https' as const
      }
    },
    {
      name: 'Odoo 13 Community',
      config: {
        host: 'odoo13.example.com',
        database: 'odoo13_db',
        username: 'admin',
        password: 'admin',
        protocol: 'http' as const,
        port: 8069
      }
    }
  ];

  for (const { name, config } of configs) {
    console.log(`\n🔄 Testing connection to ${name}...`);
    
    const adapter = new UniversalOdooAdapter(null, null, null, null, null, null, null);
    
    try {
      adapter.setConnectionConfig(config);
      await adapter.connect();
      await adapter.authenticate();
      
      const versionInfo = adapter.getVersionInfo();
      const capabilities = adapter.getCapabilities();
      
      console.log(`✅ Connected to ${name}`);
      console.log(`   Version: ${versionInfo?.series}`);
      console.log(`   Protocol: ${capabilities?.hasJsonRpc ? 'JSON-RPC' : 'XML-RPC'}`);
      console.log(`   Max batch size: ${capabilities?.maxBatchSize}`);
      
      // Test basic functionality
      const partnerCount = await adapter.execute('res.partner', 'search_count', [[]]);
      console.log(`   Total partners: ${partnerCount}`);
      
    } catch (error) {
      console.error(`❌ Failed to connect to ${name}:`, error.message);
    } finally {
      await adapter.disconnect();
    }
  }
}

// Run examples
if (require.main === module) {
  console.log('🚀 Universal Odoo Adapter Examples\n');
  
  // Uncomment the example you want to run:
  // exampleUsage();
  // multiVersionExample();
  
  console.log('💡 Uncomment the example functions to run them');
}
*/
