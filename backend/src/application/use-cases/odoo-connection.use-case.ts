import { Injectable, Logger, Inject } from '@nestjs/common';
import { IOdooAdapter } from '../../domain/repositories/odoo-adapter.interface';
import { OdooConnectionConfig, SearchReadOptions } from '../../domain/value-objects/odoo-connection-config';

@Injectable()
export class OdooConnectionUseCase {
  private readonly logger = new Logger(OdooConnectionUseCase.name);

  constructor(
    @Inject('IOdooAdapter') private readonly odooAdapter: IOdooAdapter
  ) {}

  async connect(config: OdooConnectionConfig): Promise<void> {
    try {
      this.odooAdapter.setConnectionConfig(config);
      await this.odooAdapter.connect();
      await this.odooAdapter.authenticate();
      
      this.logger.log(`Successfully connected to Odoo at ${config.host}`);
    } catch (error) {
      this.logger.error('Failed to connect to Odoo', error);
      throw error;
    }
  }

  async getVersionInfo() {
    return this.odooAdapter.getVersionInfo();
  }

  async getCapabilities() {
    return this.odooAdapter.getCapabilities();
  }

  async searchRead<T = any>(
    model: string,
    domain?: any[],
    options?: SearchReadOptions
  ): Promise<T[]> {
    try {
      return await this.odooAdapter.searchRead<T>(model, domain, options);
    } catch (error) {
      this.logger.error(`Failed to search ${model}`, error);
      throw error;
    }
  }

  async create<T = any>(model: string, values: Partial<T>): Promise<number> {
    try {
      return await this.odooAdapter.create(model, values);
    } catch (error) {
      this.logger.error(`Failed to create ${model}`, error);
      throw error;
    }
  }

  async update<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean> {
    try {
      return await this.odooAdapter.write(model, ids, values);
    } catch (error) {
      this.logger.error(`Failed to update ${model}`, error);
      throw error;
    }
  }

  async delete(model: string, ids: number[]): Promise<boolean> {
    try {
      return await this.odooAdapter.unlink(model, ids);
    } catch (error) {
      this.logger.error(`Failed to delete ${model}`, error);
      throw error;
    }
  }

  async execute(model: string, method: string, args: any[], kwargs?: any): Promise<any> {
    try {
      return await this.odooAdapter.execute(model, method, args, kwargs);
    } catch (error) {
      this.logger.error(`Failed to execute ${model}.${method}`, error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    await this.odooAdapter.disconnect();
    this.logger.log('Disconnected from Odoo');
  }
}
