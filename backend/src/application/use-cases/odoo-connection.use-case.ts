import { Injectable, Logger } from '@nestjs/common';
import {
  OdooConnectionConfig,
  SearchReadOptions,
} from '../../domain/value-objects/odoo-connection-config';
import { OdooSessionService } from '../../infrastructure/adapters/odoo/odoo-session.service';

@Injectable()
export class OdooConnectionUseCase {
  private readonly logger = new Logger(OdooConnectionUseCase.name);

  constructor(
    private readonly odooSessionService: OdooSessionService,
  ) {}

  async connect(config: OdooConnectionConfig): Promise<void> {
    try {
      await this.odooSessionService.connect(config);
      this.logger.log(`Successfully connected to Odoo at ${config.host}`);
    } catch (error) {
      this.logger.error('Failed to connect to Odoo', error);
      throw error;
    }
  }

  async getVersionInfo() {
    return this.odooSessionService.getVersionInfo();
  }

  async getCapabilities() {
    return this.odooSessionService.getCapabilities();
  }

  async searchRead<T = any>(
    model: string,
    domain?: any[],
    options?: SearchReadOptions,
  ): Promise<T[]> {
    try {
      const adapter = this.odooSessionService.getAdapter();
      return await adapter.searchRead<T>(model, domain, options);
    } catch (error) {
      this.logger.error(`Failed to search ${model}`, error);
      throw error;
    }
  }

  async create<T = any>(model: string, values: Partial<T>): Promise<number> {
    try {
      const adapter = this.odooSessionService.getAdapter();
      return await adapter.create(model, values);
    } catch (error) {
      this.logger.error(`Failed to create ${model}`, error);
      throw error;
    }
  }

  async update<T = any>(
    model: string,
    ids: number[],
    values: Partial<T>,
  ): Promise<boolean> {
    try {
      const adapter = this.odooSessionService.getAdapter();
      return await adapter.write(model, ids, values);
    } catch (error) {
      this.logger.error(`Failed to update ${model}`, error);
      throw error;
    }
  }

  async delete(model: string, ids: number[]): Promise<boolean> {
    try {
      const adapter = this.odooSessionService.getAdapter();
      return await adapter.unlink(model, ids);
    } catch (error) {
      this.logger.error(`Failed to delete ${model}`, error);
      throw error;
    }
  }

  async execute(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): Promise<any> {
    try {
      const adapter = this.odooSessionService.getAdapter();
      return await adapter.execute(model, method, args, kwargs);
    } catch (error) {
      this.logger.error(`Failed to execute ${model}.${method}`, error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    await this.odooSessionService.disconnect();
    this.logger.log('Disconnected from Odoo');
  }
}
