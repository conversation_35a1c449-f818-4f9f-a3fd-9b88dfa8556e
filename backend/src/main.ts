import { NestFactory } from '@nestjs/core';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { API_VERSIONING_CONFIG } from './infrastructure/config/api-versioning.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS
  app.enableCors();

  // Set global prefix
  app.setGlobalPrefix('api');

  // Enable API versioning
  app.enableVersioning(API_VERSIONING_CONFIG);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Swagger configuration for main API docs
  const config = new DocumentBuilder()
    .setTitle('Universal Odoo Adapter API')
    .setDescription(
      'A comprehensive API for connecting to multiple Odoo versions with automatic protocol selection',
    )
    .setVersion('1.0')
    .addTag('API Information')
    .addTag('Odoo Integration v1')
    .addTag('Odoo Integration (Legacy - Deprecated)')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Version-specific Swagger documentation
  const v1Config = new DocumentBuilder()
    .setTitle('Universal Odoo Adapter API v1')
    .setDescription('Version 1 of the Universal Odoo Adapter API')
    .setVersion('1.0')
    .addTag('Odoo Integration v1')
    .build();

  const v1Document = SwaggerModule.createDocument(app, v1Config, {
    include: [], // Will include v1 controllers
  });
  SwaggerModule.setup('api/v1/docs', app, v1Document);

  const port = process.env.PORT ?? 3000;
  await app.listen(port);

  console.log(
    `🚀 Universal Odoo Adapter API is running on: http://localhost:${port}`,
  );
  console.log(`📚 API Documentation:`);
  console.log(`   • Main docs: http://localhost:${port}/api/docs`);
  console.log(`   • v1 docs:   http://localhost:${port}/api/v1/docs`);
  console.log(`📋 API Endpoints:`);
  console.log(`   • API info:  http://localhost:${port}/api/v1/info`);
  console.log(`   • Health:    http://localhost:${port}/api/v1/info/health`);
  console.log(`   • v1 base:   http://localhost:${port}/api/v1/odoo`);
}
bootstrap();
