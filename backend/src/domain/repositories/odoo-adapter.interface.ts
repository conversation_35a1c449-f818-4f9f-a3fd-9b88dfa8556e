import {
  OdooConnectionConfig,
  OdooVersionInfo,
  OdooCapabilities,
  SearchReadOptions,
  AuthMethod,
  ProtocolType
} from '../value-objects/odoo-connection-config';

// Re-export for convenience
export { OdooCapabilities, OdooVersionInfo, SearchReadOptions };

export interface IOdooProtocol {
  readonly type: ProtocolType;
  readonly supportedMethods: string[];
  
  connect(config: OdooConnectionConfig): Promise<void>;
  authenticate(method: AuthMethod, credentials: any): Promise<number>;
  execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
  disconnect(): Promise<void>;
}

export interface IVersionAdapter {
  readonly version: string;
  readonly capabilities: OdooCapabilities;
  
  mapFields(model: string, fields: string[]): string[];
  mapDomain(domain: any[]): any[];
  mapMethod(model: string, method: string): string;
  handleResponse(response: any): any;
}

export interface IOdooAdapter {
  setConnectionConfig(config: OdooConnectionConfig): void;
  connect(): Promise<void>;
  authenticate(method?: AuthMethod, credentials?: any): Promise<number>;

  searchRead<T = any>(
    model: string,
    domain?: any[],
    options?: SearchReadOptions
  ): Promise<T[]>;

  create<T = any>(model: string, values: Partial<T>): Promise<number>;
  write<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean>;
  unlink(model: string, ids: number[]): Promise<boolean>;

  execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;

  getVersionInfo(): OdooVersionInfo | null;
  getCapabilities(): OdooCapabilities | null;

  disconnect(): Promise<void>;
}

export interface IOdooRepository {
  getAdapter(): IOdooAdapter;
  setConnectionConfig(config: OdooConnectionConfig): void;
}
