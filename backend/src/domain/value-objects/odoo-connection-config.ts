export interface OdooConnectionConfig {
  host: string;
  database: string;
  username: string;
  password: string;
  port?: number;
  protocol?: 'http' | 'https';
}

export interface OdooVersionInfo {
  major: number;
  minor: number;
  patch: number;
  series: string; // "13.0", "15.0", "17.0", "18.0"
  edition: 'community' | 'enterprise';
  serverVersion: string;
  protocolVersion: number;
}

export interface OdooCapabilities {
  hasJsonRpc: boolean;
  hasRestApi: boolean;
  hasGraphQL: boolean;
  hasWebSocket: boolean;
  hasTokenAuth: boolean;
  hasOAuth2: boolean;
  maxBatchSize: number;
  supportedAuthMethods: AuthMethod[];
}

export enum ProtocolType {
  XMLRPC = 'xmlrpc',
  JSONRPC = 'jsonrpc',
  REST = 'rest',
  GRAPHQL = 'graphql',
  WEBSOCKET = 'websocket',
}

export enum AuthMethod {
  PASSWORD = 'password',
  API_KEY = 'api_key',
  OAUTH2 = 'oauth2',
  TOKEN = 'token',
}

export interface SearchOptions {
  offset?: number;
  limit?: number;
  order?: string;
}

export interface SearchReadOptions extends SearchOptions {
  fields?: string[];
}
