import { Injectable, Logger } from '@nestjs/common';
import { UniversalOdooAdapter } from './universal-odoo-adapter';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';

@Injectable()
export class OdooSessionService {
  private readonly logger = new Logger(OdooSessionService.name);
  private static adapter: UniversalOdooAdapter | null = null;
  private static isConnected = false;
  private static connectionConfig: OdooConnectionConfig | null = null;

  constructor(
    private readonly xmlRpcProtocol: XmlRpcProtocol,
    private readonly jsonRpcProtocol: JsonRpcProtocol,
    private readonly restApiProtocol: RestApiProtocol,
    private readonly odooV18Adapter: OdooV18Adapter,
    private readonly odooV17Adapter: OdooV17Adapter,
    private readonly odooV15Adapter: OdooV15Adapter,
    private readonly odooV13Adapter: OdooV13Adapter
  ) {}

  async connect(config: OdooConnectionConfig): Promise<void> {
    try {
      // Disconnect existing connection if any
      if (OdooSessionService.isConnected && OdooSessionService.adapter) {
        await this.disconnect();
      }

      // Create new adapter instance
      OdooSessionService.adapter = new UniversalOdooAdapter(
        this.xmlRpcProtocol,
        this.jsonRpcProtocol,
        this.restApiProtocol,
        this.odooV18Adapter,
        this.odooV17Adapter,
        this.odooV15Adapter,
        this.odooV13Adapter
      );
      OdooSessionService.connectionConfig = config;

      // Connect and authenticate
      OdooSessionService.adapter.setConnectionConfig(config);
      await OdooSessionService.adapter.connect();
      await OdooSessionService.adapter.authenticate();

      OdooSessionService.isConnected = true;
      this.logger.log(`Connected to Odoo at ${config.host}`);
    } catch (error) {
      OdooSessionService.isConnected = false;
      OdooSessionService.adapter = null;
      OdooSessionService.connectionConfig = null;
      this.logger.error('Failed to connect to Odoo', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.adapter && this.isConnected) {
      try {
        await this.adapter.disconnect();
        this.logger.log('Disconnected from Odoo');
      } catch (error) {
        this.logger.error('Error during disconnect', error);
      }
    }

    this.adapter = null;
    this.isConnected = false;
    this.connectionConfig = null;
  }

  getAdapter(): UniversalOdooAdapter {
    if (!this.isConnected || !this.adapter) {
      throw new Error('Not connected to Odoo. Call connect() first.');
    }
    return this.adapter;
  }

  isSessionActive(): boolean {
    return this.isConnected && this.adapter !== null;
  }

  getConnectionConfig(): OdooConnectionConfig | null {
    return this.connectionConfig;
  }

  getVersionInfo() {
    if (!this.adapter) {
      return null;
    }
    return this.adapter.getVersionInfo();
  }

  getCapabilities() {
    if (!this.adapter) {
      return null;
    }
    return this.adapter.getCapabilities();
  }
}
