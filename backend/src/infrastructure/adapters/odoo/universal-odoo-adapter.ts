import { Injectable, Logger } from '@nestjs/common';
import { 
  IOdooAdapter, 
  IOdooProtocol, 
  IVersionAdapter 
} from '../../../domain/repositories/odoo-adapter.interface';
import { 
  OdooConnectionConfig, 
  OdooVersionInfo, 
  OdooCapabilities, 
  SearchReadOptions,
  AuthMethod,
  ProtocolType 
} from '../../../domain/value-objects/odoo-connection-config';

// Protocol imports
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';

// Version adapter imports
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';

@Injectable()
export class UniversalOdooAdapter implements IOdooAdapter {
  private readonly logger = new Logger(UniversalOdooAdapter.name);
  
  private protocol: IOdooProtocol;
  private versionAdapter: IVersionAdapter;
  private versionInfo: OdooVersionInfo | null = null;
  private capabilities: OdooCapabilities | null = null;
  private config: OdooConnectionConfig;
  
  constructor(
    private readonly xmlRpcProtocol: XmlRpcProtocol,
    private readonly jsonRpcProtocol: JsonRpcProtocol,
    private readonly restApiProtocol: RestApiProtocol,
    private readonly odooV18Adapter: OdooV18Adapter,
    private readonly odooV17Adapter: OdooV17Adapter,
    private readonly odooV15Adapter: OdooV15Adapter,
    private readonly odooV13Adapter: OdooV13Adapter
  ) {}
  
  async connect(): Promise<void> {
    if (!this.config) {
      throw new Error('Connection config not set. Call setConnectionConfig() first.');
    }
    
    try {
      // 1. Detect version first
      this.versionInfo = await this.detectVersion();
      this.logger.log(`Detected Odoo version: ${this.versionInfo.series}`);
      
      // 2. Create version adapter
      this.versionAdapter = this.createVersionAdapter(this.versionInfo);
      this.capabilities = this.versionAdapter.capabilities;
      
      // 3. Select best protocol based on capabilities
      this.protocol = this.selectOptimalProtocol();
      this.logger.log(`Selected protocol: ${this.protocol.type}`);
      
      // 4. Connect using selected protocol
      await this.protocol.connect(this.config);
      
    } catch (error) {
      this.logger.error('Failed to connect to Odoo', error);
      throw new Error(`Connection failed: ${error.message}`);
    }
  }
  
  async authenticate(method?: AuthMethod, credentials?: any): Promise<number> {
    const authMethod = method || this.selectBestAuthMethod();
    const creds = credentials || {
      database: this.config.database,
      username: this.config.username,
      password: this.config.password
    };
    
    try {
      const uid = await this.protocol.authenticate(authMethod, creds);
      this.logger.log(`Authenticated successfully with method: ${authMethod}`);
      return uid;
    } catch (error) {
      this.logger.error('Authentication failed', error);
      throw error;
    }
  }
  
  async searchRead<T = any>(
    model: string, 
    domain: any[] = [], 
    options: SearchReadOptions = {}
  ): Promise<T[]> {
    try {
      // Apply version-specific transformations
      const mappedFields = this.versionAdapter.mapFields(model, options.fields || []);
      const mappedDomain = this.versionAdapter.mapDomain(domain);
      const mappedMethod = this.versionAdapter.mapMethod(model, 'search_read');
      
      const result = await this.protocol.execute(
        model,
        mappedMethod,
        [mappedDomain],
        {
          fields: mappedFields.length > 0 ? mappedFields : undefined,
          limit: options.limit,
          offset: options.offset,
          order: options.order
        }
      );
      
      return this.versionAdapter.handleResponse(result);
    } catch (error) {
      this.logger.error(`searchRead failed for model ${model}`, error);
      throw error;
    }
  }
  
  async create<T = any>(model: string, values: Partial<T>): Promise<number> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, 'create');
      return await this.protocol.execute(model, mappedMethod, [values]);
    } catch (error) {
      this.logger.error(`create failed for model ${model}`, error);
      throw error;
    }
  }
  
  async write<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, 'write');
      return await this.protocol.execute(model, mappedMethod, [ids, values]);
    } catch (error) {
      this.logger.error(`write failed for model ${model}`, error);
      throw error;
    }
  }
  
  async unlink(model: string, ids: number[]): Promise<boolean> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, 'unlink');
      return await this.protocol.execute(model, mappedMethod, [ids]);
    } catch (error) {
      this.logger.error(`unlink failed for model ${model}`, error);
      throw error;
    }
  }
  
  async execute(model: string, method: string, args: any[], kwargs?: any): Promise<any> {
    try {
      const mappedMethod = this.versionAdapter.mapMethod(model, method);
      const result = await this.protocol.execute(model, mappedMethod, args, kwargs);
      return this.versionAdapter.handleResponse(result);
    } catch (error) {
      this.logger.error(`execute failed for ${model}.${method}`, error);
      throw error;
    }
  }
  
  getVersionInfo(): OdooVersionInfo | null {
    return this.versionInfo;
  }
  
  getCapabilities(): OdooCapabilities | null {
    return this.capabilities;
  }
  
  async disconnect(): Promise<void> {
    if (this.protocol) {
      await this.protocol.disconnect();
    }
    this.logger.log('Disconnected from Odoo');
  }
  
  setConnectionConfig(config: OdooConnectionConfig): void {
    this.config = config;
  }

  // ============= PRIVATE METHODS =============

  private async detectVersion(): Promise<OdooVersionInfo> {
    // Try multiple detection methods
    try {
      // Method 1: XML-RPC common service
      return await this.detectVersionViaXmlRpc();
    } catch (error) {
      try {
        // Method 2: JSON-RPC web client info
        return await this.detectVersionViaJsonRpc();
      } catch (error) {
        // Method 3: HTTP endpoint
        return await this.detectVersionViaHttp();
      }
    }
  }

  private async detectVersionViaXmlRpc(): Promise<OdooVersionInfo> {
    const xmlrpc = require('xmlrpc');
    const port = this.config.port || (this.config.protocol === 'https' ? 443 : 80);

    const client = xmlrpc.createClient({
      host: this.config.host,
      port,
      path: '/xmlrpc/2/common'
    });

    return new Promise((resolve, reject) => {
      client.methodCall('version', [], (error: any, value: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(this.parseVersionInfo(value));
        }
      });
    });
  }

  private async detectVersionViaJsonRpc(): Promise<OdooVersionInfo> {
    const baseUrl = `${this.config.protocol}://${this.config.host}:${this.config.port || 80}`;

    const response = await fetch(`${baseUrl}/web/webclient/version_info`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'call',
        params: {}
      })
    });

    const result = await response.json();
    return this.parseVersionInfo(result.result);
  }

  private async detectVersionViaHttp(): Promise<OdooVersionInfo> {
    const baseUrl = `${this.config.protocol}://${this.config.host}:${this.config.port || 80}`;

    const response = await fetch(`${baseUrl}/web/webclient/version_info`);
    const result = await response.json();
    return this.parseVersionInfo(result);
  }

  private parseVersionInfo(versionData: any): OdooVersionInfo {
    const version = versionData.server_version || versionData.version;
    const [major, minor, patch] = version.split('.').map(Number);

    return {
      major,
      minor,
      patch: patch || 0,
      series: `${major}.${minor}`,
      edition: this.detectEdition(versionData),
      serverVersion: version,
      protocolVersion: versionData.protocol_version || 1
    };
  }

  private detectEdition(versionData: any): 'community' | 'enterprise' {
    // Try to detect if it's enterprise edition
    const version = versionData.server_version || versionData.version || '';
    return version.includes('e') || versionData.enterprise ? 'enterprise' : 'community';
  }

  private createVersionAdapter(version: OdooVersionInfo): IVersionAdapter {
    if (version.major >= 18) return this.odooV18Adapter;
    if (version.major >= 17) return this.odooV17Adapter;
    if (version.major >= 15) return this.odooV15Adapter;
    if (version.major >= 13) return this.odooV13Adapter;

    throw new Error(`Unsupported Odoo version: ${version.series}`);
  }

  private selectOptimalProtocol(): IOdooProtocol {
    // Priority order based on capabilities and performance
    if (this.capabilities?.hasRestApi) {
      return this.restApiProtocol;
    }

    if (this.capabilities?.hasJsonRpc) {
      return this.jsonRpcProtocol;
    }

    // Fallback to XML-RPC (always available)
    return this.xmlRpcProtocol;
  }

  private selectBestAuthMethod(): AuthMethod {
    const supported = this.capabilities?.supportedAuthMethods || [];

    // Priority order
    if (supported.includes(AuthMethod.API_KEY)) return AuthMethod.API_KEY;
    if (supported.includes(AuthMethod.OAUTH2)) return AuthMethod.OAUTH2;
    if (supported.includes(AuthMethod.TOKEN)) return AuthMethod.TOKEN;

    return AuthMethod.PASSWORD; // Fallback
  }
}
