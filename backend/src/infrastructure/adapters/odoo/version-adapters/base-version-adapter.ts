import {
  IVersionAdapter,
  OdooCapabilities,
} from '../../../../domain/repositories/odoo-adapter.interface';
import { AuthMethod } from '../../../../domain/value-objects/odoo-connection-config';

export abstract class BaseVersionAdapter implements IVersionAdapter {
  abstract readonly version: string;
  abstract readonly capabilities: OdooCapabilities;

  protected fieldMappings: Record<string, Record<string, string | null>> = {};
  protected methodMappings: Record<string, string> = {
    search_read: 'search_read',
    create: 'create',
    write: 'write',
    unlink: 'unlink',
  };

  mapFields(model: string, fields: string[]): string[] {
    const modelMappings = this.fieldMappings[model] || {};
    return fields
      .map((field) => modelMappings[field] ?? field)
      .filter((field) => field !== null);
  }

  mapDomain(domain: any[]): any[] {
    // Base implementation - can be overridden by specific versions
    return domain;
  }

  mapMethod(model: string, method: string): string {
    return this.methodMappings[method] || method;
  }

  handleResponse(response: any): any {
    // Base implementation - can be overridden by specific versions
    return response;
  }

  protected setFieldMapping(
    model: string,
    mappings: Record<string, string | null>,
  ): void {
    this.fieldMappings[model] = { ...this.fieldMappings[model], ...mappings };
  }

  protected setMethodMapping(mappings: Record<string, string>): void {
    this.methodMappings = { ...this.methodMappings, ...mappings };
  }
}
