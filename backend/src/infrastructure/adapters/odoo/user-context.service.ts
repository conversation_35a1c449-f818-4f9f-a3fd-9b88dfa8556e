import { Injectable, Logger, Inject, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { UserContext } from './odoo-connection-pool.service';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';

export interface AuthenticatedUser {
  id: string;
  username: string;
  email?: string;
  sessionId?: string;
  // Add other user properties as needed
}

export interface RequestWithUser extends Request {
  user?: AuthenticatedUser;
  sessionId?: string;
  sessionID?: string; // Express session ID
  session?: any; // Express session object
}

@Injectable({ scope: Scope.REQUEST })
export class UserContextService {
  private readonly logger = new Logger(UserContextService.name);
  private userContext: UserContext | null = null;

  constructor(@Inject(REQUEST) private readonly request: RequestWithUser) {}

  /**
   * Set user context with Odoo configuration
   */
  setUserContext(odooConfig: OdooConnectionConfig): void {
    const user = this.extractUserFromRequest();
    
    this.userContext = {
      userId: user.id,
      sessionId: user.sessionId || this.generateSessionId(),
      odooConfig,
    };

    this.logger.debug(`User context set for user: ${user.id}`);
  }

  /**
   * Get current user context
   */
  getUserContext(): UserContext {
    if (!this.userContext) {
      throw new Error('User context not set. Call setUserContext() first.');
    }
    return this.userContext;
  }

  /**
   * Check if user context is set
   */
  hasUserContext(): boolean {
    return this.userContext !== null;
  }

  /**
   * Get user ID from context
   */
  getUserId(): string {
    const context = this.getUserContext();
    return context.userId;
  }

  /**
   * Get session ID from context
   */
  getSessionId(): string {
    const context = this.getUserContext();
    return context.sessionId;
  }

  /**
   * Update Odoo configuration in current context
   */
  updateOdooConfig(odooConfig: OdooConnectionConfig): void {
    if (!this.userContext) {
      throw new Error('User context not set. Call setUserContext() first.');
    }
    
    this.userContext.odooConfig = odooConfig;
    this.logger.debug(`Updated Odoo config for user: ${this.userContext.userId}`);
  }

  /**
   * Extract user information from request
   * This method should be adapted based on your authentication strategy
   */
  private extractUserFromRequest(): AuthenticatedUser {
    // Method 1: From authenticated user object (if using Passport.js or similar)
    if (this.request.user) {
      return {
        id: this.request.user.id,
        username: this.request.user.username,
        email: this.request.user.email,
        sessionId: this.request.sessionId,
      };
    }

    // Method 2: From session
    if (this.request.session && (this.request.session as any).user) {
      const sessionUser = (this.request.session as any).user;
      return {
        id: sessionUser.id,
        username: sessionUser.username,
        email: sessionUser.email,
        sessionId: this.request.sessionID || this.request.sessionId,
      };
    }

    // Method 3: From JWT token (if using JWT authentication)
    const authHeader = this.request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        // This is a simplified example - you should use proper JWT verification
        const token = authHeader.substring(7);
        const decoded = this.decodeJWT(token);
        return {
          id: decoded.sub || decoded.userId,
          username: decoded.username,
          email: decoded.email,
          sessionId: decoded.sessionId || this.generateSessionId(),
        };
      } catch (error) {
        this.logger.error('Failed to decode JWT token', error);
      }
    }

    // Method 4: From custom headers (for testing or specific use cases)
    const userId = this.request.headers['x-user-id'] as string;
    const username = this.request.headers['x-username'] as string;
    
    if (userId && username) {
      return {
        id: userId,
        username: username,
        sessionId: this.generateSessionId(),
      };
    }

    // Fallback: Generate anonymous user for development/testing
    if (process.env.NODE_ENV === 'development') {
      const anonymousId = this.generateAnonymousUserId();
      this.logger.warn(`No authenticated user found, using anonymous user: ${anonymousId}`);
      return {
        id: anonymousId,
        username: 'anonymous',
        sessionId: this.generateSessionId(),
      };
    }

    throw new Error('No authenticated user found in request');
  }

  /**
   * Decode JWT token (simplified - use proper JWT library in production)
   */
  private decodeJWT(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = Buffer.from(payload, 'base64').toString('utf-8');
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate anonymous user ID for development
   */
  private generateAnonymousUserId(): string {
    const ip = this.request.ip || this.request.connection.remoteAddress || 'unknown';
    const userAgent = this.request.headers['user-agent'] || 'unknown';
    
    // Create a simple hash of IP + User Agent for consistent anonymous ID
    const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substr(0, 8);
    return `anon_${hash}`;
  }

  /**
   * Get request metadata for logging/debugging
   */
  getRequestMetadata() {
    return {
      ip: this.request.ip,
      userAgent: this.request.headers['user-agent'],
      method: this.request.method,
      url: this.request.url,
      timestamp: new Date().toISOString(),
    };
  }
}
