import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { LRUCache } from 'lru-cache';
import { UniversalOdooAdapter } from './universal-odoo-adapter';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';

export interface UserContext {
  userId: string;
  sessionId: string;
  odooConfig: OdooConnectionConfig;
}

export interface CachedConnection {
  adapter: UniversalOdooAdapter;
  lastUsed: Date;
  connectionConfig: OdooConnectionConfig;
}

@Injectable()
export class OdooConnectionPoolService implements OnModuleDestroy {
  private readonly logger = new Logger(OdooConnectionPoolService.name);
  
  private readonly connectionPool = new LRUCache<string, CachedConnection>({
    max: 100, // Maximum 100 concurrent user connections
    ttl: 1000 * 60 * 30, // 30 minutes TTL
    dispose: async (cachedConnection, key) => {
      try {
        await cachedConnection.adapter.disconnect();
        this.logger.log(`Disposed connection for key: ${key}`);
      } catch (error) {
        this.logger.error(`Error disposing connection for key: ${key}`, error);
      }
    },
    updateAgeOnGet: true, // Reset TTL when accessed
    updateAgeOnHas: false,
  });

  constructor(
    private readonly xmlRpcProtocol: XmlRpcProtocol,
    private readonly jsonRpcProtocol: JsonRpcProtocol,
    private readonly restApiProtocol: RestApiProtocol,
    private readonly odooV18Adapter: OdooV18Adapter,
    private readonly odooV17Adapter: OdooV17Adapter,
    private readonly odooV15Adapter: OdooV15Adapter,
    private readonly odooV13Adapter: OdooV13Adapter,
  ) {}

  /**
   * Get or create an Odoo connection for a user
   */
  async getConnection(userContext: UserContext): Promise<UniversalOdooAdapter> {
    const cacheKey = this.generateCacheKey(userContext);

    // Check if connection exists and is valid
    const cachedConnection = this.connectionPool.get(cacheKey);
    if (cachedConnection) {
      this.logger.debug(`Found cached connection for user: ${userContext.userId}, validating...`);

      // Validate connection before reuse
      const isValid = await this.validateConnection(cachedConnection.adapter);
      if (isValid) {
        // Update last used timestamp
        cachedConnection.lastUsed = new Date();
        this.logger.debug(`Reusing valid connection for user: ${userContext.userId}`);
        return cachedConnection.adapter;
      } else {
        // Remove invalid connection and create new one
        this.logger.warn(`Invalid connection found for user: ${userContext.userId}, removing from pool`);
        this.connectionPool.delete(cacheKey);
      }
    }

    // Create new connection
    this.logger.log(`Creating new connection for user: ${userContext.userId}`);
    const adapter = await this.createConnectionWithRetry(userContext.odooConfig);

    // Cache the connection
    const newCachedConnection: CachedConnection = {
      adapter,
      lastUsed: new Date(),
      connectionConfig: userContext.odooConfig,
    };

    this.connectionPool.set(cacheKey, newCachedConnection);

    this.logger.log(`Connection cached for user: ${userContext.userId}. Pool size: ${this.connectionPool.size}`);
    return adapter;
  }

  /**
   * Remove a specific user's connection from the pool
   */
  async removeConnection(userContext: UserContext): Promise<void> {
    const cacheKey = this.generateCacheKey(userContext);
    const cachedConnection = this.connectionPool.get(cacheKey);
    
    if (cachedConnection) {
      try {
        await cachedConnection.adapter.disconnect();
        this.connectionPool.delete(cacheKey);
        this.logger.log(`Removed connection for user: ${userContext.userId}`);
      } catch (error) {
        this.logger.error(`Error removing connection for user: ${userContext.userId}`, error);
      }
    }
  }

  /**
   * Remove all connections for a specific user (across all Odoo instances)
   */
  async removeUserConnections(userId: string): Promise<void> {
    const keysToRemove: string[] = [];
    
    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      if (key.startsWith(`${userId}:`)) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      const cachedConnection = this.connectionPool.get(key);
      if (cachedConnection) {
        try {
          await cachedConnection.adapter.disconnect();
          this.connectionPool.delete(key);
        } catch (error) {
          this.logger.error(`Error removing connection for key: ${key}`, error);
        }
      }
    }

    this.logger.log(`Removed ${keysToRemove.length} connections for user: ${userId}`);
  }

  /**
   * Get connection pool statistics
   */
  getPoolStats() {
    return {
      size: this.connectionPool.size,
      maxSize: this.connectionPool.max,
      connections: Array.from(this.connectionPool.entries()).map(([key, cached]) => ({
        key,
        lastUsed: cached.lastUsed,
        host: cached.connectionConfig.host,
        database: cached.connectionConfig.database,
      })),
    };
  }

  /**
   * Warm up connections for frequently used configurations
   */
  async warmUpConnections(configs: Array<{ userId: string; config: OdooConnectionConfig }>): Promise<void> {
    this.logger.log(`Warming up ${configs.length} connections...`);

    const warmupPromises = configs.map(async ({ userId, config }) => {
      try {
        const userContext: UserContext = {
          userId,
          sessionId: `warmup_${Date.now()}`,
          odooConfig: config,
        };

        await this.getConnection(userContext);
        this.logger.debug(`Warmed up connection for user: ${userId}`);
      } catch (error) {
        this.logger.warn(`Failed to warm up connection for user: ${userId}`, error);
      }
    });

    await Promise.allSettled(warmupPromises);
    this.logger.log(`Connection warmup completed. Pool size: ${this.connectionPool.size}`);
  }

  /**
   * Batch operation: Execute multiple operations across different connections
   */
  async executeBatchOperations<T>(
    operations: Array<{
      userContext: UserContext;
      operation: (adapter: UniversalOdooAdapter) => Promise<T>;
    }>,
  ): Promise<Array<{ success: boolean; result?: T; error?: string; userId: string }>> {
    this.logger.log(`Executing ${operations.length} batch operations...`);

    const results = await Promise.allSettled(
      operations.map(async ({ userContext, operation }) => {
        try {
          const adapter = await this.getConnection(userContext);
          const result = await operation(adapter);
          return { success: true, result, userId: userContext.userId };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            userId: userContext.userId
          };
        }
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          error: result.reason?.message || 'Unknown error',
          userId: operations[index].userContext.userId,
        };
      }
    });
  }

  /**
   * Health check for all connections in pool
   */
  async healthCheck(): Promise<{
    totalConnections: number;
    healthyConnections: number;
    unhealthyConnections: number;
    details: Array<{ key: string; healthy: boolean; lastUsed: Date }>;
  }> {
    const connections = Array.from(this.connectionPool.entries());
    const healthChecks = await Promise.allSettled(
      connections.map(async ([key, cached]) => {
        const isHealthy = await this.validateConnection(cached.adapter);
        return { key, healthy: isHealthy, lastUsed: cached.lastUsed };
      })
    );

    const details = healthChecks.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          key: connections[index][0],
          healthy: false,
          lastUsed: connections[index][1].lastUsed,
        };
      }
    });

    const healthyCount = details.filter(d => d.healthy).length;

    return {
      totalConnections: connections.length,
      healthyConnections: healthyCount,
      unhealthyConnections: connections.length - healthyCount,
      details,
    };
  }

  /**
   * Check if a connection exists for a user
   */
  hasConnection(userContext: UserContext): boolean {
    const cacheKey = this.generateCacheKey(userContext);
    return this.connectionPool.has(cacheKey);
  }

  /**
   * Find existing connection for a user by user ID
   */
  findUserConnection(userId: string): CachedConnection | null {
    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      if (key.startsWith(`${userId}:`)) {
        return cachedConnection;
      }
    }
    return null;
  }

  /**
   * Generate cache key for user connection
   */
  private generateCacheKey(userContext: UserContext): string {
    const { userId, odooConfig } = userContext;
    // Include host, database, and username to handle multiple Odoo instances per user
    return `${userId}:${odooConfig.host}:${odooConfig.database}:${odooConfig.username}`;
  }

  /**
   * Validate if a connection is still active and authenticated
   */
  private async validateConnection(adapter: UniversalOdooAdapter): Promise<boolean> {
    try {
      // Check if adapter has version info (basic connection check)
      const versionInfo = adapter.getVersionInfo();
      if (!versionInfo) {
        this.logger.debug('Connection validation failed: no version info');
        return false;
      }

      // Check if adapter has capabilities (authentication check)
      const capabilities = adapter.getCapabilities();
      if (!capabilities) {
        this.logger.debug('Connection validation failed: no capabilities');
        return false;
      }

      // Try a simple authenticated operation to verify the connection is still valid
      try {
        // This will throw if not authenticated or connection is lost
        await adapter.searchRead('res.users', [['id', '=', 1]], { limit: 1 });
        return true;
      } catch (authError) {
        this.logger.debug('Connection validation failed: authentication test failed', authError.message);
        return false;
      }
    } catch (error) {
      this.logger.debug('Connection validation failed', error);
      return false;
    }
  }

  /**
   * Create a new Odoo adapter connection with retry logic
   */
  private async createConnectionWithRetry(
    config: OdooConnectionConfig,
    maxRetries: number = 3,
  ): Promise<UniversalOdooAdapter> {
    let lastError: Error = new Error('No attempts made');

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const adapter = await this.createConnection(config);
        this.logger.log(`Connection created successfully on attempt ${attempt}`);
        return adapter;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        this.logger.warn(`Connection attempt ${attempt} failed:`, lastError.message);

        if (attempt < maxRetries) {
          // Exponential backoff: 1s, 2s, 4s
          const delay = Math.pow(2, attempt - 1) * 1000;
          this.logger.log(`Retrying in ${delay}ms...`);
          await this.sleep(delay);
        }
      }
    }

    this.logger.error(`Failed to create connection after ${maxRetries} attempts`);
    throw lastError;
  }

  /**
   * Create a new Odoo adapter connection
   */
  private async createConnection(config: OdooConnectionConfig): Promise<UniversalOdooAdapter> {
    const adapter = new UniversalOdooAdapter(
      this.xmlRpcProtocol,
      this.jsonRpcProtocol,
      this.restApiProtocol,
      this.odooV18Adapter,
      this.odooV17Adapter,
      this.odooV15Adapter,
      this.odooV13Adapter,
    );

    try {
      adapter.setConnectionConfig(config);
      await adapter.connect();
      await adapter.authenticate();
      return adapter;
    } catch (error) {
      this.logger.error('Failed to create Odoo connection', error);
      throw error;
    }
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup stale connections (older than specified age)
   */
  async cleanupStaleConnections(maxAgeMinutes: number = 60): Promise<number> {
    const cutoffTime = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
    const keysToRemove: string[] = [];

    for (const [key, cached] of this.connectionPool.entries()) {
      if (cached.lastUsed < cutoffTime) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      const cached = this.connectionPool.get(key);
      if (cached) {
        try {
          await cached.adapter.disconnect();
          this.connectionPool.delete(key);
        } catch (error) {
          this.logger.error(`Error cleaning up stale connection: ${key}`, error);
        }
      }
    }

    if (keysToRemove.length > 0) {
      this.logger.log(`Cleaned up ${keysToRemove.length} stale connections`);
    }

    return keysToRemove.length;
  }

  /**
   * Preemptively refresh connections that are about to expire
   */
  async refreshExpiringConnections(refreshThresholdMinutes: number = 25): Promise<number> {
    const refreshTime = new Date(Date.now() - refreshThresholdMinutes * 60 * 1000);
    let refreshedCount = 0;

    for (const [key, cached] of this.connectionPool.entries()) {
      if (cached.lastUsed < refreshTime) {
        try {
          // Validate and refresh if needed
          const isValid = await this.validateConnection(cached.adapter);
          if (!isValid) {
            // Recreate connection
            const newAdapter = await this.createConnectionWithRetry(cached.connectionConfig);
            cached.adapter = newAdapter;
            cached.lastUsed = new Date();
            refreshedCount++;
            this.logger.debug(`Refreshed connection for key: ${key}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to refresh connection: ${key}`, error);
          // Remove invalid connection
          this.connectionPool.delete(key);
        }
      }
    }

    if (refreshedCount > 0) {
      this.logger.log(`Refreshed ${refreshedCount} expiring connections`);
    }

    return refreshedCount;
  }

  /**
   * Get pool metrics for monitoring
   */
  getPoolMetrics() {
    const now = new Date();
    const connections = Array.from(this.connectionPool.entries());

    const ages = connections.map(([, cached]) =>
      now.getTime() - cached.lastUsed.getTime()
    );

    return {
      size: this.connectionPool.size,
      maxSize: this.connectionPool.max,
      utilizationPercent: (this.connectionPool.size / this.connectionPool.max) * 100,
      averageAgeMinutes: ages.length > 0 ? ages.reduce((a, b) => a + b, 0) / ages.length / 60000 : 0,
      oldestConnectionMinutes: ages.length > 0 ? Math.max(...ages) / 60000 : 0,
      newestConnectionMinutes: ages.length > 0 ? Math.min(...ages) / 60000 : 0,
    };
  }

  /**
   * Cleanup all connections on module destroy
   */
  async onModuleDestroy() {
    this.logger.log('Cleaning up all Odoo connections...');

    const disconnectPromises: Promise<void>[] = [];

    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      disconnectPromises.push(
        cachedConnection.adapter.disconnect().catch((error) => {
          this.logger.error(`Error disconnecting adapter for key: ${key}`, error);
        })
      );
    }

    await Promise.allSettled(disconnectPromises);
    this.connectionPool.clear();

    this.logger.log('All Odoo connections cleaned up');
  }
}
