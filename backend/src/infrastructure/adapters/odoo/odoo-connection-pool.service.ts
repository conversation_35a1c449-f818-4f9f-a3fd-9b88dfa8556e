import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { LRUCache } from 'lru-cache';
import { UniversalOdooAdapter } from './universal-odoo-adapter';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';

export interface UserContext {
  userId: string;
  sessionId: string;
  odooConfig: OdooConnectionConfig;
}

export interface CachedConnection {
  adapter: UniversalOdooAdapter;
  lastUsed: Date;
  connectionConfig: OdooConnectionConfig;
}

@Injectable()
export class OdooConnectionPoolService implements OnModuleDestroy {
  private readonly logger = new Logger(OdooConnectionPoolService.name);
  
  private readonly connectionPool = new LRUCache<string, CachedConnection>({
    max: 100, // Maximum 100 concurrent user connections
    ttl: 1000 * 60 * 30, // 30 minutes TTL
    dispose: async (cachedConnection, key) => {
      try {
        await cachedConnection.adapter.disconnect();
        this.logger.log(`Disposed connection for key: ${key}`);
      } catch (error) {
        this.logger.error(`Error disposing connection for key: ${key}`, error);
      }
    },
    updateAgeOnGet: true, // Reset TTL when accessed
    updateAgeOnHas: false,
  });

  constructor(
    private readonly xmlRpcProtocol: XmlRpcProtocol,
    private readonly jsonRpcProtocol: JsonRpcProtocol,
    private readonly restApiProtocol: RestApiProtocol,
    private readonly odooV18Adapter: OdooV18Adapter,
    private readonly odooV17Adapter: OdooV17Adapter,
    private readonly odooV15Adapter: OdooV15Adapter,
    private readonly odooV13Adapter: OdooV13Adapter,
  ) {}

  /**
   * Get or create an Odoo connection for a user
   */
  async getConnection(userContext: UserContext): Promise<UniversalOdooAdapter> {
    const cacheKey = this.generateCacheKey(userContext);
    
    // Check if connection exists and is valid
    const cachedConnection = this.connectionPool.get(cacheKey);
    if (cachedConnection) {
      // Update last used timestamp
      cachedConnection.lastUsed = new Date();
      this.logger.debug(`Reusing connection for user: ${userContext.userId}`);
      return cachedConnection.adapter;
    }

    // Create new connection
    this.logger.log(`Creating new connection for user: ${userContext.userId}`);
    const adapter = await this.createConnection(userContext.odooConfig);
    
    // Cache the connection
    const newCachedConnection: CachedConnection = {
      adapter,
      lastUsed: new Date(),
      connectionConfig: userContext.odooConfig,
    };
    
    this.connectionPool.set(cacheKey, newCachedConnection);
    
    this.logger.log(`Connection cached for user: ${userContext.userId}. Pool size: ${this.connectionPool.size}`);
    return adapter;
  }

  /**
   * Remove a specific user's connection from the pool
   */
  async removeConnection(userContext: UserContext): Promise<void> {
    const cacheKey = this.generateCacheKey(userContext);
    const cachedConnection = this.connectionPool.get(cacheKey);
    
    if (cachedConnection) {
      try {
        await cachedConnection.adapter.disconnect();
        this.connectionPool.delete(cacheKey);
        this.logger.log(`Removed connection for user: ${userContext.userId}`);
      } catch (error) {
        this.logger.error(`Error removing connection for user: ${userContext.userId}`, error);
      }
    }
  }

  /**
   * Remove all connections for a specific user (across all Odoo instances)
   */
  async removeUserConnections(userId: string): Promise<void> {
    const keysToRemove: string[] = [];
    
    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      if (key.startsWith(`${userId}:`)) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      const cachedConnection = this.connectionPool.get(key);
      if (cachedConnection) {
        try {
          await cachedConnection.adapter.disconnect();
          this.connectionPool.delete(key);
        } catch (error) {
          this.logger.error(`Error removing connection for key: ${key}`, error);
        }
      }
    }

    this.logger.log(`Removed ${keysToRemove.length} connections for user: ${userId}`);
  }

  /**
   * Get connection pool statistics
   */
  getPoolStats() {
    return {
      size: this.connectionPool.size,
      maxSize: this.connectionPool.max,
      connections: Array.from(this.connectionPool.entries()).map(([key, cached]) => ({
        key,
        lastUsed: cached.lastUsed,
        host: cached.connectionConfig.host,
        database: cached.connectionConfig.database,
      })),
    };
  }

  /**
   * Check if a connection exists for a user
   */
  hasConnection(userContext: UserContext): boolean {
    const cacheKey = this.generateCacheKey(userContext);
    return this.connectionPool.has(cacheKey);
  }

  /**
   * Find existing connection for a user by user ID
   */
  findUserConnection(userId: string): CachedConnection | null {
    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      if (key.startsWith(`${userId}:`)) {
        return cachedConnection;
      }
    }
    return null;
  }

  /**
   * Generate cache key for user connection
   */
  private generateCacheKey(userContext: UserContext): string {
    const { userId, odooConfig } = userContext;
    // Include host, database, and username to handle multiple Odoo instances per user
    return `${userId}:${odooConfig.host}:${odooConfig.database}:${odooConfig.username}`;
  }

  /**
   * Create a new Odoo adapter connection
   */
  private async createConnection(config: OdooConnectionConfig): Promise<UniversalOdooAdapter> {
    const adapter = new UniversalOdooAdapter(
      this.xmlRpcProtocol,
      this.jsonRpcProtocol,
      this.restApiProtocol,
      this.odooV18Adapter,
      this.odooV17Adapter,
      this.odooV15Adapter,
      this.odooV13Adapter,
    );

    try {
      adapter.setConnectionConfig(config);
      await adapter.connect();
      await adapter.authenticate();
      return adapter;
    } catch (error) {
      this.logger.error('Failed to create Odoo connection', error);
      throw error;
    }
  }

  /**
   * Cleanup all connections on module destroy
   */
  async onModuleDestroy() {
    this.logger.log('Cleaning up all Odoo connections...');
    
    const disconnectPromises: Promise<void>[] = [];
    
    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      disconnectPromises.push(
        cachedConnection.adapter.disconnect().catch((error) => {
          this.logger.error(`Error disconnecting adapter for key: ${key}`, error);
        })
      );
    }

    await Promise.allSettled(disconnectPromises);
    this.connectionPool.clear();
    
    this.logger.log('All Odoo connections cleaned up');
  }
}
