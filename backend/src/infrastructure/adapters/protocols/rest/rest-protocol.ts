import { Injectable, Logger } from '@nestjs/common';
import { IOdooProtocol } from '../../../../domain/repositories/odoo-adapter.interface';
import {
  OdooConnectionConfig,
  ProtocolType,
  AuthMethod,
} from '../../../../domain/value-objects/odoo-connection-config';

@Injectable()
export class RestApiProtocol implements IOdooProtocol {
  private readonly logger = new Logger(RestApiProtocol.name);
  readonly type = ProtocolType.REST;
  readonly supportedMethods = ['GET', 'POST', 'PUT', 'DELETE'];

  private apiKey: string | null = null;
  private baseUrl: string;
  private config: OdooConnectionConfig;

  async connect(config: OdooConnectionConfig): Promise<void> {
    this.config = config;
    const port = config.port || (config.protocol === 'https' ? 443 : 80);
    // Clean host - remove protocol if present
    const cleanHost = config.host.replace(/^https?:\/\//, '');
    this.baseUrl = `${config.protocol}://${cleanHost}:${port}/api/v1`;

    this.logger.log(`Connected to Odoo REST API at ${this.baseUrl}`);
  }

  async authenticate(method: AuthMethod, credentials: any): Promise<number> {
    if (method === AuthMethod.API_KEY) {
      this.apiKey = credentials.apiKey;
      this.logger.log('Authenticated using API key');
      return credentials.uid;
    }

    try {
      // For other auth methods, get API key via login
      const response = await fetch(`${this.baseUrl}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          database: credentials.database || this.config.database,
          username: credentials.username || this.config.username,
          password: credentials.password || this.config.password,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      this.apiKey = result.api_key;
      const uid = result.uid;

      this.logger.log(`Authenticated via REST API as user ID: ${uid}`);
      return uid;
    } catch (error) {
      this.logger.error('REST API authentication failed', error);
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  async execute(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): Promise<any> {
    if (!this.apiKey) {
      throw new Error('Not authenticated. Call authenticate() first.');
    }

    try {
      const url = this.buildRestUrl(model, method, args, kwargs);
      const httpMethod = this.mapToHttpMethod(method);

      const response = await fetch(url, {
        method: httpMethod,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: httpMethod !== 'GET' ? JSON.stringify(kwargs || {}) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    } catch (error) {
      this.logger.error(
        `REST API execute failed for ${model}.${method}`,
        error,
      );
      throw new Error(`Execute failed: ${error.message}`);
    }
  }

  private buildRestUrl(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): string {
    const modelPath = model.replace('.', '/');
    let url = `${this.baseUrl}/${modelPath}`;

    // Handle specific methods
    if (method === 'search_read') {
      const params = new URLSearchParams();
      if (kwargs?.domain)
        params.append('domain', JSON.stringify(kwargs.domain));
      if (kwargs?.fields) params.append('fields', kwargs.fields.join(','));
      if (kwargs?.limit) params.append('limit', kwargs.limit.toString());
      if (kwargs?.offset) params.append('offset', kwargs.offset.toString());
      if (kwargs?.order) params.append('order', kwargs.order);

      return `${url}?${params.toString()}`;
    }

    if (method === 'write' && args.length > 0) {
      url += `/${args[0].join(',')}`; // IDs
    }

    if (method === 'unlink' && args.length > 0) {
      url += `/${args[0].join(',')}`; // IDs
    }

    return url;
  }

  private mapToHttpMethod(method: string): string {
    const methodMap: Record<string, string> = {
      search_read: 'GET',
      create: 'POST',
      write: 'PUT',
      unlink: 'DELETE',
    };

    return methodMap[method] || 'POST';
  }

  async disconnect(): Promise<void> {
    this.apiKey = null;
    this.logger.log('Disconnected from REST API');
  }
}
