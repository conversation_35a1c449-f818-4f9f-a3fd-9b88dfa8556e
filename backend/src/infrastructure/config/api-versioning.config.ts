import { VersioningType } from '@nestjs/common';

export const API_VERSIONING_CONFIG = {
  type: VersioningType.URI,
  prefix: 'v',
  defaultVersion: '1',
} as const;

export const API_VERSIONS = {
  V1: '1',
  // V2: '2', // Future versions
} as const;

export type ApiVersion = (typeof API_VERSIONS)[keyof typeof API_VERSIONS];

export interface VersionInfo {
  version: string;
  releaseDate: string;
  status: 'stable' | 'beta' | 'deprecated';
  features: string[];
  breakingChanges?: string[];
  migrationGuide?: string;
}

export const VERSION_REGISTRY: Record<ApiVersion, VersionInfo> = {
  [API_VERSIONS.V1]: {
    version: 'v1',
    releaseDate: '2025-01-26',
    status: 'stable',
    features: [
      'Universal Odoo Adapter with multi-version support',
      'Multi-protocol support (XML-RPC, JSON-RPC, REST)',
      'Automatic version detection and protocol selection',
      'Field mapping between Odoo versions',
      'CRUD operations for all Odoo models',
      'Custom method execution',
      'Comprehensive error handling',
      'Swagger documentation',
    ],
  },
};

export class ApiVersioningService {
  static getCurrentVersion(): ApiVersion {
    return API_VERSIONS.V1;
  }

  static getAvailableVersions(): ApiVersion[] {
    return Object.values(API_VERSIONS);
  }

  static getVersionInfo(version: ApiVersion): VersionInfo | undefined {
    return VERSION_REGISTRY[version];
  }

  static isVersionSupported(version: string): boolean {
    return Object.values(API_VERSIONS).includes(version as ApiVersion);
  }

  static getLatestVersion(): ApiVersion {
    const versions = this.getAvailableVersions();
    return versions[versions.length - 1];
  }

  static compareVersions(v1: string, v2: string): number {
    const version1 = parseInt(v1);
    const version2 = parseInt(v2);
    return version1 - version2;
  }
}
