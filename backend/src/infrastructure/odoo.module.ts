import { Mo<PERSON><PERSON>, MiddlewareConsumer, RequestMethod } from '@nestjs/common';

// Protocol implementations
import { XmlRpcProtocol } from './adapters/protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from './adapters/protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from './adapters/protocols/rest/rest-protocol';

// Version adapters
import { OdooV18Adapter } from './adapters/odoo/version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './adapters/odoo/version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './adapters/odoo/version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './adapters/odoo/version-adapters/odoo-v13-adapter';

// Main adapter
import { UniversalOdooAdapter } from './adapters/odoo/universal-odoo-adapter';

// Application layer
import { OdooConnectionUseCase } from '../application/use-cases/odoo-connection.use-case';

// Presentation layer
import { OdooController } from '../presentation/controllers/odoo.controller';
import { OdooV1Controller } from '../presentation/controllers/v1/odoo-v1.controller';
import { ApiVersionController } from '../presentation/controllers/api-version.controller';
import { DeprecationMiddleware } from '../presentation/middleware/deprecation.middleware';

// Domain interfaces - using string token for DI
const ODOO_ADAPTER_TOKEN = 'IOdooAdapter';

@Module({
  providers: [
    // Protocol implementations
    XmlRpcProtocol,
    JsonRpcProtocol,
    RestApiProtocol,

    // Version adapters
    OdooV18Adapter,
    OdooV17Adapter,
    OdooV15Adapter,
    OdooV13Adapter,

    // Main adapter
    UniversalOdooAdapter,
    {
      provide: ODOO_ADAPTER_TOKEN,
      useClass: UniversalOdooAdapter,
    },

    // Use cases
    OdooConnectionUseCase,
  ],
  controllers: [
    ApiVersionController,
    OdooController, // Legacy controller (will be deprecated)
    OdooV1Controller, // Version 1 controller
  ],
  exports: [ODOO_ADAPTER_TOKEN, OdooConnectionUseCase, UniversalOdooAdapter],
})
export class OdooModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(DeprecationMiddleware)
      .forRoutes({ path: 'api/odoo/*path', method: RequestMethod.ALL });
  }
}
