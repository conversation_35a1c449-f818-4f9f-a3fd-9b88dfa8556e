import { 
  Controller, 
  Post, 
  Get, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query,
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { OdooConnectionUseCase } from '../../application/use-cases/odoo-connection.use-case';
import { 
  OdooConnectionDto, 
  SearchReadDto, 
  CreateRecordDto, 
  UpdateRecordDto, 
  DeleteRecordDto 
} from '../../application/dtos/odoo-connection.dto';

@ApiTags('Odoo Integration')
@Controller('api/odoo')
export class OdooController {
  constructor(private readonly odooConnectionUseCase: OdooConnectionUseCase) {}

  @Post('connect')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Connect to Odoo instance' })
  @ApiResponse({ status: 200, description: 'Successfully connected to Odoo' })
  @ApiResponse({ status: 400, description: 'Invalid connection parameters' })
  @ApiResponse({ status: 500, description: 'Connection failed' })
  async connect(@Body() connectionDto: OdooConnectionDto) {
    await this.odooConnectionUseCase.connect(connectionDto);
    return { 
      success: true, 
      message: 'Connected to Odoo successfully',
      version: await this.odooConnectionUseCase.getVersionInfo(),
      capabilities: await this.odooConnectionUseCase.getCapabilities()
    };
  }

  @Get('version')
  @ApiOperation({ summary: 'Get Odoo version information' })
  @ApiResponse({ status: 200, description: 'Version information retrieved' })
  async getVersion() {
    return await this.odooConnectionUseCase.getVersionInfo();
  }

  @Get('capabilities')
  @ApiOperation({ summary: 'Get Odoo capabilities' })
  @ApiResponse({ status: 200, description: 'Capabilities retrieved' })
  async getCapabilities() {
    return await this.odooConnectionUseCase.getCapabilities();
  }

  @Post(':model/search')
  @ApiOperation({ summary: 'Search and read records from Odoo model' })
  @ApiParam({ name: 'model', description: 'Odoo model name (e.g., res.partner)' })
  @ApiResponse({ status: 200, description: 'Records retrieved successfully' })
  async searchRead(
    @Param('model') model: string,
    @Body() searchDto: SearchReadDto
  ) {
    const { domain, fields, limit, offset, order } = searchDto;
    return await this.odooConnectionUseCase.searchRead(
      model, 
      domain, 
      { fields, limit, offset, order }
    );
  }

  @Post(':model')
  @ApiOperation({ summary: 'Create a new record in Odoo model' })
  @ApiParam({ name: 'model', description: 'Odoo model name' })
  @ApiResponse({ status: 201, description: 'Record created successfully' })
  async create(
    @Param('model') model: string,
    @Body() createDto: CreateRecordDto
  ) {
    const recordId = await this.odooConnectionUseCase.create(model, createDto.values);
    return { 
      success: true, 
      id: recordId, 
      message: 'Record created successfully' 
    };
  }

  @Put(':model')
  @ApiOperation({ summary: 'Update records in Odoo model' })
  @ApiParam({ name: 'model', description: 'Odoo model name' })
  @ApiResponse({ status: 200, description: 'Records updated successfully' })
  async update(
    @Param('model') model: string,
    @Body() updateDto: UpdateRecordDto
  ) {
    const success = await this.odooConnectionUseCase.update(
      model, 
      updateDto.ids, 
      updateDto.values
    );
    return { 
      success, 
      message: success ? 'Records updated successfully' : 'Update failed' 
    };
  }

  @Delete(':model')
  @ApiOperation({ summary: 'Delete records from Odoo model' })
  @ApiParam({ name: 'model', description: 'Odoo model name' })
  @ApiResponse({ status: 200, description: 'Records deleted successfully' })
  async delete(
    @Param('model') model: string,
    @Body() deleteDto: DeleteRecordDto
  ) {
    const success = await this.odooConnectionUseCase.delete(model, deleteDto.ids);
    return { 
      success, 
      message: success ? 'Records deleted successfully' : 'Delete failed' 
    };
  }

  @Post(':model/execute/:method')
  @ApiOperation({ summary: 'Execute custom method on Odoo model' })
  @ApiParam({ name: 'model', description: 'Odoo model name' })
  @ApiParam({ name: 'method', description: 'Method name to execute' })
  @ApiResponse({ status: 200, description: 'Method executed successfully' })
  async execute(
    @Param('model') model: string,
    @Param('method') method: string,
    @Body() body: { args?: any[], kwargs?: any }
  ) {
    const { args = [], kwargs = {} } = body;
    const result = await this.odooConnectionUseCase.execute(model, method, args, kwargs);
    return { 
      success: true, 
      result, 
      message: 'Method executed successfully' 
    };
  }

  @Post('disconnect')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Disconnect from Odoo instance' })
  @ApiResponse({ status: 200, description: 'Disconnected successfully' })
  async disconnect() {
    await this.odooConnectionUseCase.disconnect();
    return { 
      success: true, 
      message: 'Disconnected from Odoo successfully' 
    };
  }
}
