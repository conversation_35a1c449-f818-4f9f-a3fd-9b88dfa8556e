import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('API Information')
@Controller('api')
export class ApiVersionController {
  @Get()
  @ApiOperation({ summary: 'Get API information and available versions' })
  @ApiResponse({
    status: 200,
    description: 'API information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Universal Odoo Adapter API' },
        description: { type: 'string' },
        currentVersion: { type: 'string', example: 'v1' },
        availableVersions: {
          type: 'array',
          items: { type: 'string' },
          example: ['v1'],
        },
        endpoints: {
          type: 'object',
          properties: {
            v1: { type: 'string', example: '/api/v1' },
          },
        },
        documentation: {
          type: 'object',
          properties: {
            swagger: { type: 'string', example: '/api/docs' },
            v1: { type: 'string', example: '/api/v1/docs' },
          },
        },
      },
    },
  })
  getApiInfo() {
    return {
      name: 'Universal Odoo Adapter API',
      description:
        'A comprehensive API for connecting to multiple Odoo versions with automatic protocol selection',
      currentVersion: 'v1',
      availableVersions: ['v1'],
      endpoints: {
        v1: '/api/v1',
      },
      documentation: {
        swagger: '/api/docs',
        v1: '/api/v1/docs',
      },
      features: {
        v1: [
          'Multi-version Odoo support (13, 15, 17, 18+)',
          'Multi-protocol support (XML-RPC, JSON-RPC, REST)',
          'Automatic version detection',
          'Intelligent protocol selection',
          'Field mapping between versions',
          'CRUD operations',
          'Custom method execution',
        ],
      },
      compatibility: {
        odooVersions: ['13.0', '15.0', '17.0', '18.0+'],
        protocols: ['XML-RPC', 'JSON-RPC', 'REST API'],
        authMethods: ['Password', 'API Key', 'OAuth2', 'Token'],
      },
    };
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'API is healthy' })
  getHealth() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: 'v1',
      uptime: process.uptime(),
    };
  }
}
