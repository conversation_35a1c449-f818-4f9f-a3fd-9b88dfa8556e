import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class DeprecationMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Check if the request is for a deprecated endpoint
    if (req.path.startsWith('/api/odoo') && !req.path.startsWith('/api/v')) {
      // Add deprecation headers
      res.setHeader('Deprecation', 'true');
      res.setHeader('Sunset', '2025-12-31'); // Sunset date
      res.setHeader('Link', '</api/v1/odoo>; rel="successor-version"');
      res.setHeader(
        'Warning',
        '299 - "This API version is deprecated. Please migrate to /api/v1/odoo"',
      );

      // Add custom deprecation header with migration info
      res.setHeader(
        'X-API-Deprecation-Info',
        JSON.stringify({
          deprecated: true,
          deprecatedSince: '2025-01-26',
          sunsetDate: '2025-12-31',
          newEndpoint: req.path.replace('/api/odoo', '/api/v1/odoo'),
          migrationGuide: '/api/docs#migration',
        }),
      );
    }

    next();
  }
}
