<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-inherit="mail.MessagingMenu.content" t-inherit-mode="extension">
        <xpath expr="//t[@name='threads']/NotificationItem" position="attributes">
            <attribute name="rating">message?.rating_id</attribute>
            <attribute name="onClick">(isMarkAsRead) => message?.rating_id and !isMarkAsRead ? this.openThread(thread) : this.onClickThread(isMarkAsRead, thread)</attribute>
        </xpath>
    </t>
</templates>
