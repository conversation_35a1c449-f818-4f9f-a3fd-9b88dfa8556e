# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON><PERSON> Nahmany, 2024
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> W<PERSON>zer, 2024
# NoaFarkash, 2024
# <PERSON>, 2024
# yael terner, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "\" or someone from the same company can give it a rating."
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "# דירוגים"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "<i class=\"fa fa-arrow-left me-1\"/> Back to the Homepage"
msgstr "חזרה לדף הבית"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-clock-o me-2\" aria-label=\"Create date\"/>"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-folder me-2\" aria-label=\"Open folder\"/>"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "A star"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg
msgid "Average Rating"
msgstr "דירוג ממוצע"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "דירוג ממוצע (%)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "תגובה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "לקוח"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Dissatisfied"
msgstr "לא מרוצה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "מסמך"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "מודל מסמך"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "שרשור דוא\"ל"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to share feedback on your experience:"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "דירוג מולא"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "Half a star"
msgstr "חצי כוכב"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Home"
msgstr "בית"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "מזהה"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "תמונה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image_url
msgid "Image URL"
msgstr "לינק לתמונה"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
msgid "Incorrect rating: should be 1, 3 or 5 (received %d)"
msgstr ""

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Invalid token or rating."
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 Days"
msgstr "30 ימים אחרונים"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 365 Days"
msgstr "365 ימים אחרונים"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 Days"
msgstr "7 ימים אחרונים"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "הודעה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "הדירוגים שלי"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "שם"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__none
msgid "No Rating yet"
msgstr "אין דירוג עדיין"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "No rating yet"
msgstr "אין דירוג עדיין"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Okay"
msgstr "בסדר"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Only the customer of \""
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "מסמך אב"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "דגם מסמך אב"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "שם מסמך אב"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "דגם מסמך אב קשור"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "אחוז הדירוגים השמחים"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr ""

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_id
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_id
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "דירוג"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating (/5)"
msgstr "דירוג (/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg_text
msgid "Rating Avg Text"
msgstr "דירוג טקסט ממוצע"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "דירוג משוב אחרון"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "דירוג תמונה אחרונה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "דירוג ערך אחרון"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "Rating Mixin"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "Rating Parent Mixin"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "שביעות רצון דירוג"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_text
msgid "Rating Text"
msgstr "סטטוס דירוג"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "ערך דירוג"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "כמות דירוגים"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr ""

#. module: rating
#. odoo-javascript
#: code:addons/rating/static/src/core/web/notification_item_patch.xml:0
msgid "Rating:"
msgstr ""

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_action
#: model:ir.model.fields,field_description:rating.field_account_analytic_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_calendar_event__rating_ids
#: model:ir.model.fields,field_description:rating.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_contract__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_services__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_model__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_badge__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_challenge__rating_ids
#: model:ir.model.fields,field_description:rating.field_iap_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_lunch_supplier__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_cc__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_main_attachment__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_phone__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_request__rating_ids
#: model:ir.model.fields,field_description:rating.field_phone_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_pricelist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_product__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_template__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_partner__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_users__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "דירוגים"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "דגם מסמך קשור"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "דירוגים קשורים"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "עובד"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "שם משאב"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Satisfied"
msgstr "מרוצה"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "אסימון אבטחה"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "שלח משוב"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Submitted on"
msgstr "נשלח ב"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you for your feedback!"
msgstr "תודה על תגובתך!"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "There is no rating for this object at the moment."
msgstr "אין כרגע דירוג לאובייקט זה."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "גלוי למשתמשים פנימיים בלבד"

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Wrong rating value. A rate should be between 0 and 5 (received %d)."
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "You cannot rate this"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "על ידי"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "ל"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "ב"
