# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "\" or someone from the same company can give it a rating."
msgstr "\"または同じ会社の誰かが評価を与えることができます。"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "評価数"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "<i class=\"fa fa-arrow-left me-1\"/> Back to the Homepage"
msgstr "<i class=\"fa fa-arrow-left me-1\"/>ホームページに戻る "

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-clock-o me-2\" aria-label=\"Create date\"/>"
msgstr "<i class=\"fa fa-clock-o me-2\" aria-label=\"Create date\"/>"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-folder me-2\" aria-label=\"Open folder\"/>"
msgstr "<i class=\"fa fa-folder me-2\" aria-label=\"Open folder\"/>"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "A star"
msgstr "星"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg
msgid "Average Rating"
msgstr "平均評価"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "平均評価（％）"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "コメント"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "作成者"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "顧客"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "表示名"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Dissatisfied"
msgstr "不満"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "ドキュメント"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "ドキュメントモデル"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "Eメールスレッド"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to share feedback on your experience:"
msgstr "あなたのご経験についてのフィードバックをお気軽にお寄せ下さい:"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "記述された評価"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "グループ化"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "Half a star"
msgstr "半分の星"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr "サブタイプの構成とは関係なく、パブリック/ポータルユーザーに非表示にします。"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Home"
msgstr "ホーム"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "画像"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image_url
msgid "Image URL"
msgstr "画像URL"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
msgid "Incorrect rating: should be 1, 3 or 5 (received %d)"
msgstr "不正な評価: 1、3 または 5である必要があります。 (受けた評価: %d)"

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Invalid token or rating."
msgstr "無効なトークンまたは評価"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 Days"
msgstr "過去30日"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 365 Days"
msgstr "過去365日"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 Days"
msgstr "過去7日"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "メッセージ"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "自分の評価"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "名称"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__none
msgid "No Rating yet"
msgstr "未評価"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "No rating yet"
msgstr "未評価"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Okay"
msgstr "OK"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Only the customer of \""
msgstr "次の顧客のみです\""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "親ドキュメント"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "親ドキュメントモデル"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "親ドキュメント名"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr "親ホルダ"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr "親参照"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "親関連ドキュメントモデル"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "高評価割合"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr "評価オペレータ"

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_id
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_id
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "評価"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating (/5)"
msgstr "評価 (/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg_text
msgid "Rating Avg Text"
msgstr "評価平均テキスト"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "評価前回のフィードバック"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "評価前回の画像"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "評価前回の数値"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "評価ミックスイン"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "評価親Mixin"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "評価満足度"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_text
msgid "Rating Text"
msgstr "評価テキスト"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "評価値"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "評価数"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr "評価は0から5の間にして下さい。"

#. module: rating
#. odoo-javascript
#: code:addons/rating/static/src/core/web/notification_item_patch.xml:0
msgid "Rating:"
msgstr "評価:"

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_action
#: model:ir.model.fields,field_description:rating.field_account_analytic_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_calendar_event__rating_ids
#: model:ir.model.fields,field_description:rating.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_contract__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_services__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_model__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_badge__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_challenge__rating_ids
#: model:ir.model.fields,field_description:rating.field_iap_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_lunch_supplier__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_cc__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_main_attachment__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_phone__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_request__rating_ids
#: model:ir.model.fields,field_description:rating.field_phone_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_pricelist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_product__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_template__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_partner__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_users__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "評価"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "関連ドキュメントモデル"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "関連評価"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "リソース"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr "リソース参照"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "リソース名"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Satisfied"
msgstr "満足"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "セキュリティトークン"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "フィードバック送信"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Submitted on"
msgstr "実行日"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr "当社のサービスの評価を頂きありがとうございます！"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you for your feedback!"
msgstr "ご意見頂きありがとうございます！"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "There is no rating for this object at the moment."
msgstr "現時点ではこのオブジェクトの評価はありません。"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "内部参照のみ"

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Wrong rating value. A rate should be between 0 and 5 (received %d)."
msgstr "誤った評価値。評価は0から5の間にして下さい(受け取った値 %d)"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "You cannot rate this"
msgstr "これを評価することはできません"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "by"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "for"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "on"
