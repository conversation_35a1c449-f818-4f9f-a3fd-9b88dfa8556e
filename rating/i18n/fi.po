# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <miu<PERSON><PERSON>@gmail.com>, 2024
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# J<PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>vant<PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "\" or someone from the same company can give it a rating."
msgstr "\" tai joku samasta yrityksestä voi antaa sille arvosanan."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "# arvostelua"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "<i class=\"fa fa-arrow-left me-1\"/> Back to the Homepage"
msgstr "<i class=\"fa fa-arrow-left me-1\"/> Takaisin kotisivulle"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-clock-o me-2\" aria-label=\"Create date\"/>"
msgstr "<i class=\"fa fa-clock-o me-2\" aria-label=\"Luontipäivä\"/>"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-folder me-2\" aria-label=\"Open folder\"/>"
msgstr "<i class=\"fa fa-folder me-2\" aria-label=\"Avaa kansio\"/>"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "A star"
msgstr "Tähti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg
msgid "Average Rating"
msgstr "Keskimääräinen arvio"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Keskimääräinen arvio (%)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "Kommentti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "Asiakas"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Dissatisfied"
msgstr "Tyytymätön"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "Dokumentti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "Dokumentin malli"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "Sähköpostiviestiketju"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to share feedback on your experience:"
msgstr "Voit antaa palautetta kokemuksistasi:"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "Täytetty luokitus"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "Half a star"
msgstr "Puolikas tähti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Piilota julkisille / portaalin käyttäjille, riippumatta alatyypin "
"asetuksista."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Home"
msgstr "Etusivu"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "Kuva"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image_url
msgid "Image URL"
msgstr "Kuvan URL"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
msgid "Incorrect rating: should be 1, 3 or 5 (received %d)"
msgstr "Virheellinen luokitus: pitäisi olla 1, 3 tai 5 (sai %d)"

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Invalid token or rating."
msgstr "Pääsytunniste tai luokitus on virheellinen."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 Days"
msgstr "Viim. 30 päivää"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 365 Days"
msgstr "Viimeiset 365 päivää"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 Days"
msgstr "Viimeiset 7 päivää"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "Viesti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "Omat luokitukseni"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "Nimi"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__none
msgid "No Rating yet"
msgstr "Ei vielä arvosanaa"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "No rating yet"
msgstr "Ei vielä arvosanaa"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Okay"
msgstr "OK"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Only the customer of \""
msgstr "Vain asiakas \""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "Ylemmän tason asiakirja"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "Ylemmän tason asiakirjan malli"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "Ylemmän tason asiakirjan nimi"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr "Ylemmän tason haltija"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr "Ylemmän tason viite"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "Ylempään tasoon liittyvä asiakirjamalli"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Tyytyväisten arvioiden prosenttiosuus"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr "Arvioitu operaattori"

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_id
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_id
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "Arvostelu"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating (/5)"
msgstr "Luokitus (/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg_text
msgid "Rating Avg Text"
msgstr "Kekismääräisen luokituksen teksti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Arvion viimeisin palaute"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "Arvion viimeisin kuva"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "Arvion viimeisin arvo"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "Arvioinnin Mixin"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "Täytetty luokitus"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Arvion tyytyväisyys"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_text
msgid "Rating Text"
msgstr "Luokitusteksti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "Rating Value"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "Arvioiden määrä"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr "Luokituksen tulisi olla 0 ja 5 välillä"

#. module: rating
#. odoo-javascript
#: code:addons/rating/static/src/core/web/notification_item_patch.xml:0
msgid "Rating:"
msgstr "Arvio:"

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_action
#: model:ir.model.fields,field_description:rating.field_account_analytic_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_calendar_event__rating_ids
#: model:ir.model.fields,field_description:rating.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_contract__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_services__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_model__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_badge__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_challenge__rating_ids
#: model:ir.model.fields,field_description:rating.field_iap_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_lunch_supplier__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_cc__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_main_attachment__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_phone__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_request__rating_ids
#: model:ir.model.fields,field_description:rating.field_phone_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_pricelist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_product__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_template__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_partner__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_users__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "Arviointi"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "Liittyvä dokumenttimalli"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "Related ratings"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "Resurssi"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr "Resurssiviite"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "Resurssin nimi"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Satisfied"
msgstr "Tyytyväinen"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "Turvatunnus"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "Lähetä palautetta"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Submitted on"
msgstr "Lähetetty"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr "Kiitos, että arvioit palveluitamme!"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you for your feedback!"
msgstr "Kiitos palautteestasi!"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "There is no rating for this object at the moment."
msgstr "Tällä hetkellä tälle kohteelle ei ole luokitusta."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "Näkyy vain sisäisesti"

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Wrong rating value. A rate should be between 0 and 5 (received %d)."
msgstr ""
"Väärä luokitusarvo. Arvosanan pitäisi olla välillä 0-5 (vastaanotettu %d)."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "You cannot rate this"
msgstr "Et voi arvioida tätä"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "kirjoittaja"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "tekijänä"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "on"
