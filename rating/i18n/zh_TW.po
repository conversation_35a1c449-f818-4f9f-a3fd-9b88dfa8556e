# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "\" or someone from the same company can give it a rating."
msgstr "\"或者,來自同一家公司的人可以給它一個評級。"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "評分數目"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "<i class=\"fa fa-arrow-left me-1\"/> Back to the Homepage"
msgstr "<i class=\"fa fa-arrow-left me-1\"/> 返回主頁"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-clock-o me-2\" aria-label=\"Create date\"/>"
msgstr "<i class=\"fa fa-clock-o me-2\" aria-label=\"建立日期\"/>"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "<i class=\"fa fa-folder me-2\" aria-label=\"Open folder\"/>"
msgstr "<i class=\"fa fa-folder me-2\" aria-label=\"開啟資料夾\"/>"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "A star"
msgstr "一粒星"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg
msgid "Average Rating"
msgstr "平均評分"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "平均評分（%）"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "評論"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "客戶"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ko
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Dissatisfied"
msgstr "不滿意"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "文件"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "單據模型"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "電郵對話串"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to share feedback on your experience:"
msgstr "歡迎分享您的體驗反饋:"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "已填寫的評分"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "分組依據"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban_stars
msgid "Half a star"
msgstr "半顆星"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr "對公共/網站登入會員使用者隱藏，獨立於子類型設定."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Home"
msgstr "首頁"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "識別號"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "圖片"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image_url
msgid "Image URL"
msgstr "圖像網址"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
msgid "Incorrect rating: should be 1, 3 or 5 (received %d)"
msgstr "不正確的評分: 必須為1，3或5 (已收到%d)"

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Invalid token or rating."
msgstr "無效權杖或評級。"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 Days"
msgstr "過去30天"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 365 Days"
msgstr "過去的365天內"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 Days"
msgstr "過去7天"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "消息"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_ids
msgid "Messages"
msgstr "訊息"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "我的評分"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "名稱"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__none
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__none
msgid "No Rating yet"
msgstr "尚未評分"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "No rating yet"
msgstr "尚未評分"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__ok
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Okay"
msgstr "Okay"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "Only the customer of \""
msgstr "只限以下項目的客戶:"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "上級單據"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "上級文件模型"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "上級單據名稱"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr "上級持有人"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr "上級參照"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "上級相關單據模型"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "滿意評分百分比"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr "評分操作"

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_id
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_id
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "評分"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating (/5)"
msgstr "評分 (/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg_text
msgid "Rating Avg Text"
msgstr "平均評分文字"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "最新回饋評分"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "最新評分圖像"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "最新評分值"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "綜合評分"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "綜合上級評分"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "評級滿意度"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_text
msgid "Rating Text"
msgstr "評分文字"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "評分值"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "評分數"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr "評分應在 0 到 5 之間"

#. module: rating
#. odoo-javascript
#: code:addons/rating/static/src/core/web/notification_item_patch.xml:0
msgid "Rating:"
msgstr "評分:"

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_action
#: model:ir.model.fields,field_description:rating.field_account_analytic_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_calendar_event__rating_ids
#: model:ir.model.fields,field_description:rating.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_contract__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_log_services__rating_ids
#: model:ir.model.fields,field_description:rating.field_fleet_vehicle_model__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_badge__rating_ids
#: model:ir.model.fields,field_description:rating.field_gamification_challenge__rating_ids
#: model:ir.model.fields,field_description:rating.field_iap_account__rating_ids
#: model:ir.model.fields,field_description:rating.field_lunch_supplier__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_cc__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_main_attachment__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_thread_phone__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_equipment_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_maintenance_request__rating_ids
#: model:ir.model.fields,field_description:rating.field_phone_blacklist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_category__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_pricelist__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_product__rating_ids
#: model:ir.model.fields,field_description:rating.field_product_template__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_partner__rating_ids
#: model:ir.model.fields,field_description:rating.field_res_users__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "評分"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "相關的單據模型"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "相關評分"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "資源"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr "資源參照"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "資源名稱"

#. module: rating
#. odoo-python
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__discuss_channel__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__product_template__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__project_task__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_mixin__rating_avg_text__top
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model:ir.model.fields.selection,name:rating.selection__slide_channel__rating_avg_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Satisfied"
msgstr "滿意"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "安全金鑰"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "發送回饋"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Submitted on"
msgstr "提交於"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr "感謝您評價我們的服務!"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you for your feedback!"
msgstr "感謝你的回應意見！"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_action
msgid "There is no rating for this object at the moment."
msgstr "這個對象目前沒有評分."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "僅內部可見"

#. module: rating
#. odoo-python
#: code:addons/rating/models/mail_thread.py:0
msgid "Wrong rating value. A rate should be between 0 and 5 (received %d)."
msgstr "評分值錯誤, 評分應介於 0 和 5 之間（收到%d）。"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_invalid_partner
msgid "You cannot rate this"
msgstr "你不可對此評分"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "由"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "給予"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "於"
