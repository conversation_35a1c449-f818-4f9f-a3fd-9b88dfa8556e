<?xml version="1.0" encoding="UTF-8"?>
<odoo>
        <!-- External page : thanks message -->
        <template id="rating_external_page_view" name="Thanks for your Feedback">
            <t t-call="web.frontend_layout">
                <div class="o_rating_page_thank_you d-flex flex-column justify-content-center">
                    <div class="container py-5">
                        <div class="oe_structure" id="oe_structure_rating_header_thanks_you"/>
                        <h3 class="text-center mt-4 pb-2">Thank you for your feedback!</h3>
                        <a t-att-href="web_base_url" class="d-block text-decoration-none text-center mb-4">
                            <i class="fa fa-arrow-left me-1"/> Back to the Homepage
                        </a>
                        <div class="oe_structure" id="oe_structure_rating_footer_thanks_you"/>
                    </div>
                </div>
            </t>
        </template>

        <!-- External page: Invalid partner rating -->
        <template id="rating_external_page_invalid_partner" name="Not allows to rating">
            <t t-call="web.frontend_layout">
                <section class="mt128">
                    <div class="container">
                        <div class="row" >
                            <div class="col-lg-4">
                                <img class="img img-fluid mx-auto" src="/rating/static/src/img/503.svg" alt=""/>
                            </div>
                            <div class="col-lg-8 text-lg-start">
                                <h1>You cannot rate this <t t-out="model_name"/></h1>
                                <p>
                                    Only the customer of "<t t-out="name"/>" or someone from the same company can give it a rating.
                                </p>
                            </div>
                            <hr class="my-5 opacity-100"/>
                            <div class="mb128 col-lg-12">
                                <ul class="list-inline text-center">
                                    <li>
                                        <a t-att-href="web_base_url" class="btn btn-primary">Home</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>
            </t>
        </template>

        <!-- External page: rate and submit feedback -->
        <template id="rating_external_page_submit" name="Rate our Services">
            <t t-call="web.frontend_layout">
                <div class="container mb-5 mt-4 o_rating_page_submit">
                    <div class="oe_structure" id="oe_structure_rate_submit_header"/>
                    <div class="row text-center justify-content-center">
                        <h1 class="col-12 mt-5">Thank you for rating our services!</h1>
                        <form class="col-md-6" t-attf-action="/rate/#{token}/submit_feedback" method="post">
                            <div class="btn-group row flex-nowrap justify-content-center w-100 mt-5"
                                 role="group"
                                 data-bs-toggle="buttons">
                                <t t-foreach="rate_names" t-as="rate_name">
                                    <input type="radio" name="rate"
                                           class="btn-check"
                                           t-attf-id="rate_{{rate_name}}"
                                           t-att-value="rate_name"
                                           t-att-checked="rate == rate_name"/>
                                    <label t-attf-class="col p-4 btn o_rating_label shadow-none transition-base {{rate == rate_name and 'active' or ''}}"
                                           t-att-for="'rate_%s' % (rate_name)">
                                        <img t-attf-src='/rating/static/src/img/rating_#{rate_name}.svg'
                                             t-att-alt="rate_name_value"
                                             t-att-title="rate_name_value"
                                             t-attf-class="o_{{rate_name_value.lower()}}"/>
                                    </label>
                                </t>
                            </div>
                            <p class="mt-5">
                                Feel free to share feedback on your experience:
                            </p>
                            <input type="hidden" name="csrf_token"
                                   t-att-value="request.csrf_token()"/>
                            <textarea class="form-control" name="feedback" rows="8"
                                      t-att-value="rating.feedback"></textarea>
                            <button type="submit" class="btn btn-primary mt-4"
                                    style="margin-top:8px;">Send Feedback</button>
                        </form>
                        <div class="oe_structure" id="oe_structure_rate_submit_footer"/>
                    </div>
                </div>
            </t>
        </template>
</odoo>
