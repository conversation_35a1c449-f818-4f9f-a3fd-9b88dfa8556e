# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_ubl_cii
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:23+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"%s should have a KVK or OIN number: the Peppol e-address (EAS) should be "
"'0106' or '0190'."
msgstr ""
"%s devrait avoir un numéro KVK ou OIN : l'adresse électronique Peppol (EAS) "
"devrait être '0106' ou '0190'."

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr "1.0"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_facturx_export_22
msgid "42"
msgstr "42"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "A payment of %s was detected."
msgstr "Un paiement de %s a été détecté."

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_a_nz
msgid "A-NZ BIS Billing 3.0"
msgstr "A-NZ BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move_send
msgid "Account Move Send"
msgstr "Envoi de l'écriture comptable"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9923
msgid "Albania VAT"
msgstr "TVA Albanie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9922
msgid "Andorra VAT"
msgstr "TVA Andorre"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Articles 226 items 11 to 15 Directive 2006/112/EN"
msgstr "Article 226 points 11 à 15 Directive 2006/112/CE"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"At least one of the following fields %(field_list)s is required on "
"%(record)s."
msgstr ""
"Au moins un des champs suivants %(field_list)s est requis sur %(record)s."

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_id
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_id
msgid "Attachment"
msgstr "Pièce jointe"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0151
msgid "Australia ABN"
msgstr "ABN Australie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9914
msgid "Austria UID"
msgstr "UID Autriche"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9915
msgid "Austria VOKZ"
msgstr "VOKZ Autriche"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_bis3
msgid "BIS Billing 3.0"
msgstr "BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_a_nz
msgid "BIS Billing 3.0 A-NZ"
msgstr "BIS Billing 3.0 A-NZ"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_sg
msgid "BIS Billing 3.0 SG"
msgstr "BIS Billing 3.0 SG"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_de
msgid "BIS3 DE (XRechnung)"
msgstr "BIS3 DE (XRechnung)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0208
msgid "Belgian Company Registry"
msgstr "Registre de commerce belge"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9925
msgid "Belgian VAT number"
msgstr "Numéro de TVA belge"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9924
msgid "Bosnia and Herzegovina VAT"
msgstr "TVA Bosnie-Herzégovine"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9926
msgid "Bulgaria VAT"
msgstr "TVA Bulgarie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9913
msgid "Business Registers Network"
msgstr "Business Registers Network"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Check Invoice(s)"
msgstr "Vérifier les facture(s)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Check Partner(s)"
msgstr "Vérifier le(s) partenaire(s)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""
"Code utilisé pour identifier le Point de terminaison pour BIS Billing 3.0 et ses dérivés.\n"
"             Liste disponible sur https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0210
msgid "Codice Fiscale"
msgstr "Codice Fiscale"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0201
msgid "Codice Univoco Unità Organizzativa iPA"
msgstr "Codice Univoco Unità Organizzativa iPA"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_common
msgid ""
"Common functions for EDI documents: generate the data, the constraints, etc"
msgstr ""
"Fonctions communes aux documents EDI : générer les données, les contraintes,"
" etc."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
msgid "Conditional cash/payment discount"
msgstr "Escompte conditionnel"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Configure"
msgstr "Configurer"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve a partner corresponding to '%s'. A new partner was "
"created."
msgstr ""
"Impossible de trouver un partenaire correspondant à '%s'. Un nouveau "
"partenaire a été créé."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve currency: %s. Did you enable the multicurrency option and"
" activate the currency?"
msgstr ""
"Impossible de récupérer la devise : %s. Avez-vous activé l'option des "
"devises multiples et activé la devise ?"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Could not retrieve the tax: %(amount)s %% for line '%(line)s'."
msgstr ""
"Impossible de récupérer la taxe : %(amount)s %% pour la ligne '%(line)s'."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Could not retrieve the tax: %(tax_percentage)s %% for line '%(line)s'."
msgstr ""
"Impossible de récupérer la taxe : %(tax_percentage)s %% pour la ligne "
"'%(line)s'."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve the tax: %s for the document level allowance/charge."
msgstr ""
"Impossible de récupérer la taxe : %s pour le niveau de l'allocation/frais du"
" document."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9934
msgid "Croatia VAT"
msgstr "TVA Croatie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9928
msgid "Cyprus VAT"
msgstr "TVA Chypre"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9929
msgid "Czech Republic VAT"
msgstr "TVA République tchèque"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0060
msgid "DUNS Number"
msgstr "Numéro DUNS"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0184
msgid "Denmark CVR"
msgstr "CVR Danemark"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0096
msgid "Denmark P"
msgstr "P Danemark"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0198
msgid "Denmark SE"
msgstr "SE Danemark"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0130
msgid "Directorates of the European Commission"
msgstr "Directorates of the European Commission"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_efff
msgid "E-FFF (BE)"
msgstr "E-FFF (BE)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0088
msgid "EAN Location Code"
msgstr "Code de localisation EAN"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr "EN 16931"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "Each invoice line shall have one and only one tax."
msgstr "Chaque ligne de facture doit comporter une et une seule taxe."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "Each invoice line should have a product or a label."
msgstr "Chaque ligne de facture doit comporter un produit ou un libellé."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Each invoice line should have at least one tax."
msgstr "Chaque ligne de facture doit comporter au moins une taxe."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr "Erreurs survenues lors de la création du document EDI (format : %s):"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0191
msgid "Estonia Company code"
msgstr "Code d’entreprise Estonie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9931
msgid "Estonia VAT"
msgstr "TVA Estonie"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Export outside the EU"
msgstr "Exportation hors UE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__facturx
msgid "Factur-X (CII)"
msgstr "Factur-X (CII)"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_cii
msgid "Factur-x/XRechnung CII 2.2.0"
msgstr "Factur-x/XRechnung CII 2.2.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0037
msgid "Finland LY-tunnus"
msgstr "LY-tunnus Finlande"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0216
msgid "Finland OVT code"
msgstr "Code OVT Finlande"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0213
msgid "Finland VAT"
msgstr "TVA Finlande"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"For intracommunity supply, the actual delivery date or the invoicing period "
"should be included."
msgstr ""
"Pour la livraison intracommunautaire, la date de livraison réelle ou la "
"période de facturation doit être incluse."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "For intracommunity supply, the delivery address should be included."
msgstr ""
"Pour la livraison intracommunautaire, l'adresse de livraison doit être "
"indiquée."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Format used to import the invoice: %s"
msgstr "Format utilisé pour importer la facture : %s"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0002
msgid "France SIRENE"
msgstr "SIRENE France"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0009
msgid "France SIRET"
msgstr "SIRET France"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9957
msgid "France VAT"
msgstr "TVA France"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0209
msgid "GS1 identification keys"
msgstr "Clés d’identification GS1"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0204
msgid "Germany Leitweg-ID"
msgstr "Leitweg-ID Allemagne"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9930
msgid "Germany VAT"
msgstr "TVA Allemagne"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9933
msgid "Greece VAT"
msgstr "TVA Grèce"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9910
msgid "Hungary VAT"
msgstr "TVA Hongrie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0196
msgid "Iceland Kennitala"
msgstr "Kennitala Islande"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0202
msgid "Indirizzo di Posta Elettronica Certificata"
msgstr "Indirizzo di Posta Elettronica Certificata"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Intra-Community supply"
msgstr "Livraison intracommunautaire"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr "Facture générée par Odoo"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9935
msgid "Ireland VAT"
msgstr "TVA Irlande"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__is_peppol_edi_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__is_peppol_edi_format
msgid "Is Peppol Edi Format"
msgstr "Est au format Edi Peppol"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__is_ubl_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__is_ubl_format
msgid "Is Ubl Format"
msgstr "Est au format UBL"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0097
msgid "Italia FTI"
msgstr "Italia FTI"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0211
msgid "Italia Partita IVA"
msgstr "Italia Partita IVA"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0221
msgid "Japan IIN"
msgstr "IIN Japon"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0188
msgid "Japan SST"
msgstr "SST Japon"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9919
msgid "Kennziffer des Unternehmensregisters"
msgstr "Kennziffer des Unternehmensregisters"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9939
msgid "Latvia VAT"
msgstr "TVA Lettonie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0199
msgid "Legal Entity Identifier (LEI)"
msgstr "Legal Entity Identifier (LEI)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9936
msgid "Liechtenstein VAT"
msgstr "TVA Liechtenstein"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0200
msgid "Lithuania JAK"
msgstr "JAK Lituanie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9937
msgid "Lithuania VAT"
msgstr "TVA Lituanie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9938
msgid "Luxembourg VAT"
msgstr "TVA Luxembourg"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9942
msgid "Macedonia VAT"
msgstr "TVA Macédoine"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0230
msgid "Malaysia"
msgstr "Malaisie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9943
msgid "Malta VAT"
msgstr "TVA Malte"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9940
msgid "Monaco VAT"
msgstr "TVA Monaco"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9941
msgid "Montenegro VAT"
msgstr "TVA Monténégro"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__nlcius
msgid "NLCIUS"
msgstr "NLCIUS"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0106
msgid "Netherlands KvK"
msgstr "KvK Pays-Bas"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0190
msgid "Netherlands OIN"
msgstr "OIN Pays-Bas"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9944
msgid "Netherlands VAT"
msgstr "TVA Pays-Bas"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"No gross price, net price nor line subtotal amount found for line in xml"
msgstr "Aucun prix brut, prix net ou sous-total trouvé pour la ligne en xml"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0192
msgid "Norway Org.nr."
msgstr "Org.nr. Norvège"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr "Odoo"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Peppol Address"
msgstr "Adresse Peppol"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid "Peppol Endpoint"
msgstr "Point de terminaison Peppol"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_eas
msgid "Peppol e-address (EAS)"
msgstr "Peppol e-address (EAS)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid ""
"Please add a Recipient bank in the 'Other Info' tab to generate a complete "
"file."
msgstr ""
"Veuillez ajouter un compte bancaire destinataire dans l'onglet « Autres "
"informations » pour générer un fichier complet."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Please configure your company to generate a complete XML file."
msgstr ""
"Veuillez configurer votre entreprise pour générer un fichier XML complet."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Please fill in your VAT or Peppol Address"
msgstr "Veuillez remplir votre numéro de TVA ou votre adresse Peppol"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9945
msgid "Poland VAT"
msgstr "TVA Pologne"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9946
msgid "Portugal VAT"
msgstr "TVA Portugal"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9947
msgid "Romania VAT"
msgstr "TVA Roumanie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9918
msgid "S.W.I.F.T"
msgstr "S.W.I.F.T"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0142
msgid "SECETI Object Identifiers"
msgstr "SECETI Object Identifiers"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_sg
msgid "SG BIS Billing 3.0"
msgstr "SG BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_nl
msgid "SI-UBL 2.0 (NLCIUS)"
msgstr "SI-UBL 2.0 (NLCIUS)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0135
msgid "SIA Object Identifiers"
msgstr "SIA Object Identifiers"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9951
msgid "San Marino VAT"
msgstr "TVA Saint-Marin"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9948
msgid "Serbia VAT"
msgstr "TVA Serbie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0195
msgid "Singapore UEN"
msgstr "UEN Singapour"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9950
msgid "Slovakia VAT"
msgstr "TVA Slovaquie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9949
msgid "Slovenia VAT"
msgstr "TVA Slovénie"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9920
msgid "Spain VAT"
msgstr "TVA Espagne"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0007
msgid "Sweden Org.nr."
msgstr "Org.nr. Suède"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9955
msgid "Sweden VAT"
msgstr "TVA Suède"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0183
msgid "Swiss UIDB"
msgstr "UIDB Suisse"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9927
msgid "Swiss VAT"
msgstr "TVA Suisse"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Tax '%(tax_name)s' is invalid: %(error_message)s"
msgstr "La taxe' %(tax_name)s' est invalide : %(error_message)s"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid ""
"The Peppol endpoint is not valid. It should contain exactly 10 digits "
"(Company Registry number).The expected format is: **********"
msgstr ""
"Le point de terminaison Peppol n'est pas valide. Il doit contenir exactement"
" 10 chiffres (numéro d'entreprise de la société). Le format à respecter est "
"le suivant : **********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid "The Peppol endpoint is not valid. The expected format is: **********"
msgstr ""
"Le point de terminaison Peppol n'est pas valide. Le format attendu est : "
"**********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid ""
"The Peppol endpoint is not valid. The expected format is: **************"
msgstr ""
"Le point de terminaison Peppol n'est pas valide. Le format attendu est : "
"**************"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"The VAT number of the supplier does not seem to be valid. It should be of "
"the form: NO179728982MVA."
msgstr ""
"Le numéro de TVA du fournisseur ne semble pas valide. Il devrait être de la "
"forme : NO179728982MVA."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "The VAT of the %s should be prefixed with its country code."
msgstr "La TVA de %s doit être précédée du code du pays. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "The country is required for the %s."
msgstr "Le pays est requis pour %s."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The currency '%s' is not active."
msgstr "La devise '%s' n'est pas active."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The element %(record)s is required on %(field_list)s."
msgstr "L'élément %(record)s est requis sur %(field_list)s."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The field %(field)s is required on %(record)s."
msgstr "Le champ %(field)s est requis sur %(record)s."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"The field 'Sanitized Account Number' is required on the Recipient Bank."
msgstr ""
"Le champ 'Numéro de compte nettoyé' est requis pour la banque destinataire."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
msgid ""
"The invoice has been converted into a credit note and the quantities have "
"been reverted."
msgstr ""
"La facture a été convertie en avoir et les quantités ont été inversées."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9952
msgid "Turkey VAT"
msgstr "TVA Turquie"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_20
msgid "UBL 2.0"
msgstr "UBL 2.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_21
msgid "UBL 2.1"
msgstr "UBL 2.1"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_bis3
msgid "UBL BIS Billing 3.0.12"
msgstr "UBL BIS Billing 3.0.12"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0193
msgid "UBL.BE party identifier"
msgstr "UBL.BE party identifier"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_file
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_file
msgid "UBL/CII File"
msgstr "Fichier UBL/CII"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9959
msgid "USA EIN"
msgstr "EIN USA"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""
"Identifiant unique utilisé par BIS Billing 3.0 et ses dérivés, également "
"connu comme 'ID du point de terminaison'."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9932
msgid "United Kingdom VAT"
msgstr "TVA Royaume-Uni"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9953
msgid "Vatican VAT"
msgstr "TVA Vatican"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "View Invoice(s)"
msgstr "Voir les facture(s)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "View Partner(s)"
msgstr "Voir le(s) partenaire(s)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"When the Canary Island General Indirect Tax (IGIC) applies, the tax rate on "
"each invoice line should be greater than 0."
msgstr ""
"Lorsque l'impôt direct général des Canaries (IGIC) s'applique, le taux "
"d'imposition de chaque ligne de facture doit être supérieur à 0."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move.py:0
msgid "XML UBL"
msgstr "XML UBL"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__xrechnung
msgid "XRechnung CIUS"
msgstr "XRechnung CIUS"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"You should include at least one tax per invoice line. [BR-CO-04]-Each "
"Invoice line (BG-25) shall be categorized with an Invoiced item VAT category"
" code (BT-151)."
msgstr ""
"Vous devez inclure au moins une taxe par ligne de facture. [BR-CO-04]-Chaque"
" ligne de facture (BG-25) doit être classée avec un code de la catégorie de "
"TVA de l'article facturé (BT-151)."

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Your endpoint"
msgstr "Votre point de terminaison"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__invoice_edi_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__invoice_edi_format
msgid "eInvoice format"
msgstr "format eInvoice"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr "factur-x.xml"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr "fx"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
