# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_ubl_cii
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:23+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"%s should have a KVK or OIN number: the Peppol e-address (EAS) should be "
"'0106' or '0190'."
msgstr ""
"%s should have a KVK or OIN number: the Peppol e-address (EAS) should be "
"'0106' or '0190'."

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr "1.0"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_facturx_export_22
msgid "42"
msgstr "42"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "A payment of %s was detected."
msgstr "تم رصد دفع بقيمة %s "

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_a_nz
msgid "A-NZ BIS Billing 3.0"
msgstr "A-NZ BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move_send
msgid "Account Move Send"
msgstr "إرسال حركة الحساب "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9923
msgid "Albania VAT"
msgstr "Albania VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9922
msgid "Andorra VAT"
msgstr "Andorra VAT"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Articles 226 items 11 to 15 Directive 2006/112/EN"
msgstr "Articles 226 items 11 to 15 Directive 2006/112/EN"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"At least one of the following fields %(field_list)s is required on "
"%(record)s."
msgstr ""
"حقل واحد على الأقل من الحقول التالية %(field_list)s مطلوب في %(record)s. "

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_id
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_id
msgid "Attachment"
msgstr "مرفق"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0151
msgid "Australia ABN"
msgstr "Australia ABN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9914
msgid "Austria UID"
msgstr "Austria UID"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9915
msgid "Austria VOKZ"
msgstr "Austria VOKZ"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_bis3
msgid "BIS Billing 3.0"
msgstr "BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_a_nz
msgid "BIS Billing 3.0 A-NZ"
msgstr "BIS Billing 3.0 A-NZ"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__ubl_sg
msgid "BIS Billing 3.0 SG"
msgstr "BIS Billing 3.0 SG"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_de
msgid "BIS3 DE (XRechnung)"
msgstr "BIS3 DE (XRechnung)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0208
msgid "Belgian Company Registry"
msgstr "Belgian Company Registry"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9925
msgid "Belgian VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9924
msgid "Bosnia and Herzegovina VAT"
msgstr "Bosnia and Herzegovina VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9926
msgid "Bulgaria VAT"
msgstr "Bulgaria VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9913
msgid "Business Registers Network"
msgstr "Business Registers Network"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Check Invoice(s)"
msgstr "تحقق من الفواتير "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Check Partner(s)"
msgstr "التحقق من الوكيل (الوكلاء) "

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0210
msgid "Codice Fiscale"
msgstr "Codice Fiscale"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0201
msgid "Codice Univoco Unità Organizzativa iPA"
msgstr "Codice Univoco Unità Organizzativa iPA"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_common
msgid ""
"Common functions for EDI documents: generate the data, the constraints, etc"
msgstr ""
"الوظائف المعتادة لمستندات نظام تبادل المستندات إلكترونياً: إنشاء البيانات، "
"التقييدات، إلخ "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
msgid "Conditional cash/payment discount"
msgstr "خصم مشروط للنقد/الدفع "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Configure"
msgstr "تهيئة "

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve a partner corresponding to '%s'. A new partner was "
"created."
msgstr ""
"Could not retrieve a partner corresponding to '%s'. A new partner was "
"created."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve currency: %s. Did you enable the multicurrency option and"
" activate the currency?"
msgstr ""
"تعذر إحضار العملة: %s. هل قمت بتمكين خيار العملات المتعددة وتفعيل العملة؟ "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Could not retrieve the tax: %(amount)s %% for line '%(line)s'."
msgstr "Could not retrieve the tax: %(amount)s %% for line '%(line)s'."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Could not retrieve the tax: %(tax_percentage)s %% for line '%(line)s'."
msgstr ""
"Could not retrieve the tax: %(tax_percentage)s %% for line '%(line)s'."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"Could not retrieve the tax: %s for the document level allowance/charge."
msgstr ""
"Could not retrieve the tax: %s for the document level allowance/charge."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9934
msgid "Croatia VAT"
msgstr "Croatia VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9928
msgid "Cyprus VAT"
msgstr "Cyprus VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9929
msgid "Czech Republic VAT"
msgstr "Czech Republic VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0060
msgid "DUNS Number"
msgstr "DUNS Number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0184
msgid "Denmark CVR"
msgstr "Denmark CVR"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0096
msgid "Denmark P"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0198
msgid "Denmark SE"
msgstr "Denmark SE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0130
msgid "Directorates of the European Commission"
msgstr "Directorates of the European Commission"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_efff
msgid "E-FFF (BE)"
msgstr "E-FFF (BE)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0088
msgid "EAN Location Code"
msgstr "EAN Location Code"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr "EN 16931"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "Each invoice line shall have one and only one tax."
msgstr "يجب أن يكون لكل بند فاتورة ضريبة واحدة فقط لا غير. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "Each invoice line should have a product or a label."
msgstr "يجب أن يحتوي كل بند فاتورة على منتج أو بطاقة عنوان. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Each invoice line should have at least one tax."
msgstr "يجب أن يكون لكل بند فاتورة ضريبة واحدة على الأقل. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr ""
"حدثت أخطاء أثناء إنشاء مستند نظام تبادل المستندات تلقائياً (الصيغة: %s): "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0191
msgid "Estonia Company code"
msgstr "Estonia Company code"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9931
msgid "Estonia VAT"
msgstr "Estonia VAT"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Export outside the EU"
msgstr "التصدير خارج الاتحاد الأوروبي "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__facturx
msgid "Factur-X (CII)"
msgstr "Factur-X (CII)"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_cii
msgid "Factur-x/XRechnung CII 2.2.0"
msgstr "Factur-x/XRechnung CII 2.2.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0037
msgid "Finland LY-tunnus"
msgstr "Finland LY-tunnus"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0216
msgid "Finland OVT code"
msgstr "Finland OVT code"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0213
msgid "Finland VAT"
msgstr "Finland VAT"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"For intracommunity supply, the actual delivery date or the invoicing period "
"should be included."
msgstr ""
"لعمليات التوريد داخل المجتمع، يجب أن يشمل تاريخ التوصيل الفعلي أو فترة "
"الفوترة. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "For intracommunity supply, the delivery address should be included."
msgstr "لعمليات التوريد داخل المجتمع، يجب أن يشمل عنوان التوصيل. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Format used to import the invoice: %s"
msgstr "التنسيق المستخدم لاستيراد الفاتورة:%s "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0002
msgid "France SIRENE"
msgstr "France SIRENE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0009
msgid "France SIRET"
msgstr "France SIRET"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9957
msgid "France VAT"
msgstr "France VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0209
msgid "GS1 identification keys"
msgstr "GS1 identification keys"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0204
msgid "Germany Leitweg-ID"
msgstr "Germany Leitweg-ID"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9930
msgid "Germany VAT"
msgstr "Germany VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9933
msgid "Greece VAT"
msgstr "Greece VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9910
msgid "Hungary VAT"
msgstr "Hungary VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0196
msgid "Iceland Kennitala"
msgstr "Iceland Kennitala"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0202
msgid "Indirizzo di Posta Elettronica Certificata"
msgstr "Indirizzo di Posta Elettronica Certificata"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Intra-Community supply"
msgstr "التوريد بين المجتمعات "

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr "تم إنشاء الفاتورة من قِبَل أودو "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9935
msgid "Ireland VAT"
msgstr "Ireland VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__is_peppol_edi_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__is_peppol_edi_format
msgid "Is Peppol Edi Format"
msgstr "Is Peppol Edi Format"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__is_ubl_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__is_ubl_format
msgid "Is Ubl Format"
msgstr "تنسيق Ubl "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0097
msgid "Italia FTI"
msgstr "Italia FTI"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0211
msgid "Italia Partita IVA"
msgstr "Italia Partita IVA"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0221
msgid "Japan IIN"
msgstr "Japan IIN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0188
msgid "Japan SST"
msgstr "Japan SST"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9919
msgid "Kennziffer des Unternehmensregisters"
msgstr "Kennziffer des Unternehmensregisters"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9939
msgid "Latvia VAT"
msgstr "Latvia VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0199
msgid "Legal Entity Identifier (LEI)"
msgstr "Legal Entity Identifier (LEI)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9936
msgid "Liechtenstein VAT"
msgstr "Liechtenstein VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0200
msgid "Lithuania JAK"
msgstr "Lithuania JAK"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9937
msgid "Lithuania VAT"
msgstr "Lithuania VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9938
msgid "Luxembourg VAT"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9942
msgid "Macedonia VAT"
msgstr "Macedonia VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0230
msgid "Malaysia"
msgstr "ماليزيا"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9943
msgid "Malta VAT"
msgstr "Malta VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9940
msgid "Monaco VAT"
msgstr "Monaco VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9941
msgid "Montenegro VAT"
msgstr "Montenegro VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__nlcius
msgid "NLCIUS"
msgstr "NLCIUS"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0106
msgid "Netherlands KvK"
msgstr "Netherlands KvK"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0190
msgid "Netherlands OIN"
msgstr "Netherlands OIN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9944
msgid "Netherlands VAT"
msgstr "Netherlands VAT"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid ""
"No gross price, net price nor line subtotal amount found for line in xml"
msgstr ""
"لم يتم العثور على السعر الإجمالي أو السعر الصافي أو الإجمالي الفرعي للبند في"
" ملف xml "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0192
msgid "Norway Org.nr."
msgstr "Norway Org.nr."

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr "أودو"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Peppol Address"
msgstr "Peppol Address"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid "Peppol Endpoint"
msgstr "Peppol Endpoint"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_eas
msgid "Peppol e-address (EAS)"
msgstr "Peppol e-address (EAS)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid ""
"Please add a Recipient bank in the 'Other Info' tab to generate a complete "
"file."
msgstr ""
"Please add a Recipient bank in the 'Other Info' tab to generate a complete "
"file."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Please configure your company to generate a complete XML file."
msgstr "Please configure your company to generate a complete XML file."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "Please fill in your VAT or Peppol Address"
msgstr "Please fill in your VAT or Peppol Address"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9945
msgid "Poland VAT"
msgstr "Poland VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9946
msgid "Portugal VAT"
msgstr "Portugal VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9947
msgid "Romania VAT"
msgstr "Romania VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9918
msgid "S.W.I.F.T"
msgstr "S.W.I.F.T"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0142
msgid "SECETI Object Identifiers"
msgstr "SECETI Object Identifiers"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_sg
msgid "SG BIS Billing 3.0"
msgstr "SG BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_nl
msgid "SI-UBL 2.0 (NLCIUS)"
msgstr "SI-UBL 2.0 (NLCIUS)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0135
msgid "SIA Object Identifiers"
msgstr "SIA Object Identifiers"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9951
msgid "San Marino VAT"
msgstr "San Marino VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9948
msgid "Serbia VAT"
msgstr "Serbia VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0195
msgid "Singapore UEN"
msgstr "Singapore UEN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9950
msgid "Slovakia VAT"
msgstr "Slovakia VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9949
msgid "Slovenia VAT"
msgstr "Slovenia VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9920
msgid "Spain VAT"
msgstr "Spain VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0007
msgid "Sweden Org.nr."
msgstr "Sweden Org.nr."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9955
msgid "Sweden VAT"
msgstr "Sweden VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0183
msgid "Swiss UIDB"
msgstr "Swiss UIDB"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9927
msgid "Swiss VAT"
msgstr "Swiss VAT"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "Tax '%(tax_name)s' is invalid: %(error_message)s"
msgstr "Tax '%(tax_name)s' is invalid: %(error_message)s"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid ""
"The Peppol endpoint is not valid. It should contain exactly 10 digits "
"(Company Registry number).The expected format is: **********"
msgstr ""
"The Peppol endpoint is not valid. It should contain exactly 10 digits "
"(Company Registry number).The expected format is: **********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid "The Peppol endpoint is not valid. The expected format is: **********"
msgstr "The Peppol endpoint is not valid. The expected format is: **********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
msgid ""
"The Peppol endpoint is not valid. The expected format is: **************"
msgstr ""
"The Peppol endpoint is not valid. The expected format is: **************"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid ""
"The VAT number of the supplier does not seem to be valid. It should be of "
"the form: NO179728982MVA."
msgstr ""
"يبدو أن رقم ضريبة القيمة المضافة للمورّد غير صالح. يجب أن يكون بالصيغة "
"التالية: NO179728982MVA. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "The VAT of the %s should be prefixed with its country code."
msgstr "The VAT of the %s should be prefixed with its country code."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
msgid "The country is required for the %s."
msgstr "الدولة مطلوبة لـ %s. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The currency '%s' is not active."
msgstr "العملة '%s' غير نشطة. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The element %(record)s is required on %(field_list)s."
msgstr "The element %(record)s is required on %(field_list)s."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
msgid "The field %(field)s is required on %(record)s."
msgstr "The field %(field)s is required on %(record)s."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"The field 'Sanitized Account Number' is required on the Recipient Bank."
msgstr "الحقل 'رقم الحساب السليم' مطلوب في الحساب البنكي للمستلم. "

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
msgid ""
"The invoice has been converted into a credit note and the quantities have "
"been reverted."
msgstr "لقد تم تحويل الفاتورة إلى إشعار دائن وقد تمت إعادة الكميات. "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9952
msgid "Turkey VAT"
msgstr "Turkey VAT"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_20
msgid "UBL 2.0"
msgstr "UBL 2.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_21
msgid "UBL 2.1"
msgstr "UBL 2.1"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_bis3
msgid "UBL BIS Billing 3.0.12"
msgstr "UBL BIS Billing 3.0.12"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0193
msgid "UBL.BE party identifier"
msgstr "UBL.BE party identifier"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_file
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_file
msgid "UBL/CII File"
msgstr "ملف UBL/CII "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9959
msgid "USA EIN"
msgstr "USA EIN"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""
"المعرف الفريد المستخدم بواسطة BIS Billing 3.0 ومشتقاتها، والتي تُعرف أيضاً "
"بـ 'Endpoint ID'. "

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9932
msgid "United Kingdom VAT"
msgstr "United Kingdom VAT"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9953
msgid "Vatican VAT"
msgstr "Vatican VAT"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "View Invoice(s)"
msgstr "View Invoice(s)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move_send.py:0
msgid "View Partner(s)"
msgstr "View Partner(s)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"When the Canary Island General Indirect Tax (IGIC) applies, the tax rate on "
"each invoice line should be greater than 0."
msgstr ""
"When the Canary Island General Indirect Tax (IGIC) applies, the tax rate on "
"each invoice line should be greater than 0."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_move.py:0
msgid "XML UBL"
msgstr "XML UBL"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__invoice_edi_format__xrechnung
msgid "XRechnung CIUS"
msgstr "XRechnung CIUS"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
msgid ""
"You should include at least one tax per invoice line. [BR-CO-04]-Each "
"Invoice line (BG-25) shall be categorized with an Invoiced item VAT category"
" code (BT-151)."
msgstr ""
"عليك أن تشمل ضريبة واحدة على الأقل لكل بند فاتورة. [BR-CO-04]-يجب أن يوضع كل"
" بند فاتورة (BG-25) في فئة، مع كود فئة ضريبة العنصر المفوتر (BT-151). "

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Your endpoint"
msgstr "Your endpoint"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__invoice_edi_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__invoice_edi_format
msgid "eInvoice format"
msgstr "تنسيق الفاتورة الإلكترونية "

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr "factur-x.xml"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr "fx"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
