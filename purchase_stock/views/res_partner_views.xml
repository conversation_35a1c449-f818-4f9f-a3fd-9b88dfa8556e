<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_partner_view_purchase_buttons_inherit" model="ir.ui.view">
            <field name="name">res.partner.purchase.stock.form.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="purchase.res_partner_view_purchase_buttons"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='purchase_order_count']/.." position="after">
                    <button class="oe_stat_button" name="%(action_purchase_vendor_delay_report)d" type="action"
                        groups="purchase.group_purchase_user"
                        icon="fa-truck"
                        context="{'search_default_partner_id': id}">
                        <div class="o_form_field o_stat_info">
                            <div class="o_row" invisible="on_time_rate &lt; 0">
                                <span class="o_stat_value">
                                    <field string="On-time Rate" name="on_time_rate" widget="integer"/>
                                </span>
                                <span class="o_stat_value">%</span>
                            </div>
                            <div class="o_stat_value" invisible="on_time_rate &gt;= 0">
                                No data yet
                            </div>
                            <span class="o_stat_text">On-time Rate</span>
                        </div>
                    </button>
                </xpath>
            </field>
    </record>
</odoo>
