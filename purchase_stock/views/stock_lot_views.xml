<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_production_lot_view_form" model="ir.ui.view">
        <field name="name">stock.production.lot.view.form</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_button_box')]/button" position="before">
                <button class="oe_stat_button" name="action_view_po"
                        type="object" icon="fa-credit-card" help="Purchase Orders"
                        invisible="purchase_order_count == 0 or not display_complete">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="purchase_order_count" widget="statinfo" nolabel="1" class="mr4"/>
                        </span>
                        <span class="o_stat_text">Purchases</span>
                    </div>
                </button>
            </xpath>
            <xpath expr="//group[@name='main_group']" position="after">
                <group>
                    <field name="purchase_order_ids" widget="many2many"
                           readonly="True"
                           invisible="not purchase_order_ids or display_complete">
                        <list>
                            <field name="name"/>
                            <field name="partner_id" readonly="state in ['cancel', 'done', 'purchase']"/>
                            <field name="date_order" readonly="state in ['cancel', 'done', 'purchase']"/>
                            <field name="state" column_invisible="True"/>
                        </list>
                    </field>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
