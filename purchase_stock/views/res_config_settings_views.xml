<?xml version="1.0" encoding="utf-8"?>
<odoo>

	<record id="res_config_settings_view_form_purchase" model="ir.ui.view">
		<field name="name">res.config.settings.view.form.inherit.purchase</field>
		<field name="model">res.config.settings</field>
		<field name="priority" eval="25"/>
		<field name="inherit_id" ref="purchase.res_config_settings_view_form_purchase"/>
		<field name="arch" type="xml">
			<xpath expr="//app[@name='purchase']" position="inside">
				<field name="is_installed_sale" invisible="1"/>
				<block title="Logistics" invisible="not is_installed_sale" name="request_vendor_setting_container">
					<setting title="This adds a dropshipping route to apply on products in order to request your vendors to deliver to your customers. A product to dropship will generate a purchase request for quotation once the sales order confirmed. This is a on-demand flow. The requested delivery address will be the customer delivery address and not your warehouse." help="Request your vendors to deliver to your customers"
							 documentation="/applications/inventory_and_mrp/inventory/shipping/operation/dropshipping.html">
						<field name="module_stock_dropshipping"/>
					</setting>
				</block>
			</xpath>
		</field>
	</record>

	<record id="res_config_settings_view_form_stock" model="ir.ui.view">
		<field name="name">res.config.settings.view.form.inherit.purchase.stock</field>
		<field name="model">res.config.settings</field>
		<field name="inherit_id" ref="stock.res_config_settings_view_form"/>
		<field name="arch" type="xml">
			<xpath expr="//block[@id='schedule_info']" position="attributes">
				<attribute name="invisible">0</attribute>
			</xpath>
			<div id="purchase_po_lead" position="replace">
				<setting company_dependent="1" help="Schedule request for quotations earlier to avoid delays" documentation="/applications/inventory_and_mrp/inventory/management/planning/scheduled_dates.html" title="Margin of error for vendor lead times. When the system generates Purchase Orders for reordering products,they will be scheduled that many days earlier to cope with unexpected vendor delays." name="schedule_receivings_setting_container">
					<field name="use_po_lead"/>
					<div class="content-group">
						<div class="mt16" invisible="not use_po_lead">
							<span>Move forward expected request creation date by <field name="po_lead" class="oe_inline"/> days</span>
						</div>
					</div>
				</setting>
				<setting company_dependent="1" help="Days needed to confirm a PO">
					<field name="days_to_purchase" class="oe_inline"/><span> days</span>
				</setting>
			</div>
		</field>
	</record>

</odoo>
