# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_stock
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "% On-Time Delivery"
msgstr "% de entrega a tiempo"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d días"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_text\">On-time Rate</span>"
msgstr "<span class=\"o_stat_text\">Tasa de entrega a tiempo</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Compras</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_value\">%</span>"
msgstr "<span class=\"o_stat_value\">%</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "<span invisible=\"on_time_rate &gt;= 0\">No On-time Delivery Data</span>"
msgstr ""
"<span invisible=\"on_time_rate &gt;= 0\">No hay información de las entregas "
"a tiempo</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "<span> days</span>"
msgstr "<span> días</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_view_kanban_catalog_purchase_only
msgid "<span>Forecasted: </span>"
msgstr "<span>Previsto: </span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Dirección de envío:</strong>"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"La cantidad recibida se puede calcular en automático con el mecanismo según la configuración del producto:\n"
"- Manual: la cantidad se establece de forma manual en la línea\n"
"- Movimientos de inventario: la cantidad corresponde a las recolecciones confirmadas\n"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_rule__action
msgid "Action"
msgstr "Acción"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__effective_date
msgid "Arrival"
msgstr "Llegada"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock.py:0
#: model:ir.model.fields.selection,name:purchase_stock.selection__stock_rule__action__buy
#: model:stock.route,name:purchase_stock.route_warehouse0_buy
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_stock_rule
msgid "Buy"
msgstr "Comprar"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_pull_id
msgid "Buy rule"
msgstr "Regla de compra"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "Buy to Resupply"
msgstr "Comprar para reabastecer"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__effective_date
msgid "Completion date of the first receipt order."
msgstr "Fecha de finalización de la primera orden de entrega."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "Corresponding receipt not found."
msgstr "No se encontró el recibo correspondiente."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__created_purchase_line_ids
msgid "Created Purchase Order Lines"
msgstr "Líneas de la orden de compra creadas"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_move.py:0
msgid "Currency exchange rate difference"
msgstr "Diferencia en tasa de cambio de divisa"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__product_description_variants
msgid "Custom Description"
msgstr "Descripción personalizada"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__days_to_arrive
msgid "Days To Arrive"
msgstr "Días en llegar"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Days needed to confirm a PO"
msgstr "Días necesarios para confirmar una orden de compra"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,help:purchase_stock.field_res_config_settings__days_to_purchase
msgid "Days needed to confirm a PO, define when a PO should be validated"
msgstr ""
"Días que se necesitan para confirmar una orden de compra, define cuándo se "
"debería de validar la orden de compra"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__days_to_purchase
msgid "Days to Purchase"
msgstr "Días para la compra"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__delay_pass
msgid "Delay Pass"
msgstr "Pase de retraso"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_type_id
msgid "Deliver To"
msgstr "Entregar a"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,help:purchase_stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"Si cuenta con los módulos necesarios, podrá definir la ruta del producto: si"
" se comprará, se fabricará, se reabastecerá bajo pedido, etc."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "Tipo de ubicación de destino"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_dest_ids
msgid "Downstream moves alt"
msgstr "Movimiento de salida alt"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "Dirección de entrega"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "Triangulación"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__date
msgid "Effective Date"
msgstr "Fecha efectiva"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "Effective Date Last Year"
msgstr "Fecha efectiva del último año"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__days_to_arrival
msgid "Effective Days To Arrival"
msgstr "Días efectivos para la llegada"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "Excepciones ocurridas en las órdenes de compra:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s):"
msgstr "Excepciones:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Forecast Report"
msgstr "Reporte de pronóstico"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__forecasted_issue
msgid "Forecasted Issue"
msgstr "Emisión pronosticada"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__full
msgid "Fully Received"
msgstr "Recibido totalmente"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Generate the draft vendor bill."
msgstr "Genere el borrador de la factura de proveedor."

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Go back to the purchase order to generate the vendor bill."
msgstr "Regrese a la orden de compra para generar la factura del proveedor."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__id
msgid "ID"
msgstr "ID"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoming_picking_count
msgid "Incoming Shipment count"
msgstr "Número de envíos entrantes"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Incoming Shipments"
msgstr "Envíos entrantes"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoterm_location
msgid "Incoterm Location"
msgstr "Ubicación del incoterm"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__is_shipped
msgid "Is Shipped"
msgstr "Se envió"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__is_installed_sale
msgid "Is the Sale Module Installed"
msgstr "¿El módulo Ventas está instalado?"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__last_purchase_date
msgid "Last Purchase"
msgstr "Última compra"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_procurement_group__purchase_line_ids
msgid "Linked Purchase Order Lines"
msgstr "Líneas de la orden de compra vinculadas"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__location_final_id
msgid "Location from procurement"
msgstr "Ubicación del aprovisionamiento"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr "Logística"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "Número de serie/lote"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Manual actions may be needed."
msgstr "Es posible que se necesiten acciones manuales."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Margen de error para los plazos de entrega del proveedor. Cuando el sistema "
"genera órdenes de compra para reordenar productos, se programarán con estos "
"días de anticipación en caso de que haya demoras inesperadas."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Regla de inventario mínimo"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Move forward expected request creation date by"
msgstr "Adelantar las fechas de creación previstas por"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Next transfer(s) impacted:"
msgstr "Siguiente(s) traslado(s) afectado(s):"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "No data yet"
msgstr "Todavía no hay información"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_help_message_template
msgid "No receipt yet! Automate them with purchase orders."
msgstr "Todavía no hay recepciones. Automatícelas con las órdenes de compra."

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__pending
msgid "Not Received"
msgstr "No recibido"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_move.py:0
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo no puede generar los asientos anglosajones. El valor total de %s es "
"cero."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_view_graph
msgid "On-Time Delivery"
msgstr "Entrega a tiempo"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__on_time_rate
msgid "On-Time Delivery Rate"
msgstr "Tasa de entrega a tiempo"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_on_time
msgid "On-Time Quantity"
msgstr "Cantidad a tiempo"

#. module: purchase_stock
#: model:ir.actions.act_window,name:purchase_stock.action_purchase_vendor_delay_report
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "On-time Delivery"
msgstr "Entrega a tiempo"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "On-time Rate"
msgstr "Tasa de puntualidad "

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__orderpoint_id
msgid "Orderpoint"
msgstr "Punto de la orden"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_users__on_time_rate
msgid ""
"Over the past x days; the number of products received on time divided by the"
" number of ordered products.x is either the System Parameter "
"purchase_stock.on_time_delivery_days or the default 365"
msgstr ""
"Durante los últimos x días; el número de productos recibidos a tiempo divido"
" entre el número de products.x ordenados es el parámetro "
"purchase_stock.on_time_delivery_days del sistema o el predeterminado 365."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__purchase_order_line_ids
msgid "PO Lines"
msgstr "Líneas de orden de compra"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__partial
msgid "Partially Received"
msgstr "Recibido parcialmente"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_category__property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Cuenta de diferencia de precio"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Process all the receipt quantities."
msgstr "Procesar todas las cantidades recibidas"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_procurement_group
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__group_id
msgid "Procurement Group"
msgstr "Grupo de aprovisionamiento"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__group_id
msgid "Procurement group that generated this line"
msgstr "Grupo de aprovisionamiento que generó esta línea"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_template
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__product_id
msgid "Product"
msgstr "Producto"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_category
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__category_id
msgid "Product Category"
msgstr "Categoría del producto"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_replenish
msgid "Product Replenish"
msgstr "Reabastecimiento de producto"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "Mixin de reabastecimiento de producto"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__product_supplier_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.warehouse_orderpoint_search_inherit
msgid "Product Supplier"
msgstr "Proveedor del producto"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_product
msgid "Product Variant"
msgstr "Variante del producto"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__propagate_cancel
msgid "Propagate cancellation"
msgstr "Propagar cancelación"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__purchase_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__purchase_line_ids
msgid "Purchase Lines"
msgstr "Líneas de compra"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order
msgid "Purchase Order"
msgstr "Orden de compra"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__purchase_line_id
msgid "Purchase Order Line"
msgstr "Línea de la orden de compra"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_lot__purchase_order_ids
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__purchase_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_help_message_template
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "Purchase Orders"
msgstr "Órdenes de compra"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_report
msgid "Purchase Report"
msgstr "Reporte de compras"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "Purchase Security Lead Time"
msgstr "Plazo de seguridad para compras"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__purchase_visibility_days
msgid "Purchase Visibility Days"
msgstr "Días de visibilidad de compras"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_lot__purchase_order_count
msgid "Purchase order count"
msgstr "Número de órdenes de compra"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Escriba una dirección si desea entregar directamente desde el proveedor al "
"cliente. De lo contrario, déjelo en blanco para hacer la entrega en su "
"propia empresa."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receipt"
msgstr "Recepción"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__receipt_status
msgid "Receipt Status"
msgstr "Estado de entrega"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receive Products"
msgstr "Recibir productos"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Receive the ordered products."
msgstr "Recibir los productos ordenados"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Método de cantidad recibida"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_ids
msgid "Receptions"
msgstr "Recepciones"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__receipt_status
msgid ""
"Red: Late\n"
"            Orange: To process today\n"
"            Green: On time"
msgstr ""
"Rojo: con demora\n"
"            Naranja: por procesar hoy\n"
"            Verde: a tiempo"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr "Solicite a sus proveedores que entreguen a sus clientes"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/purchase_stock_forecasted/forecasted_details.xml:0
msgid "Requests for quotation"
msgstr "Solicitudes de cotización"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_ids
msgid "Reservation"
msgstr "Reserva"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Recolección de devolución"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__route_ids
msgid "Routes"
msgstr "Rutas"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Schedule request for quotations earlier to avoid delays"
msgstr ""
"Programar la solicitud de cotizaciones con anticipación para evitar demoras"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_supplierinfo_replenishment_tree_view
msgid "Set as Supplier"
msgstr "Configurado como proveedor"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__show_set_supplier_button
msgid "Show Set Supplier Button"
msgstr "Mostrar el botón de configurar proveedor"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_replenish__show_vendor
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenish_mixin__show_vendor
msgid "Show Vendor"
msgstr "Mostrar proveedor"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__show_supplier
msgid "Show supplier column"
msgstr "Mostrar la columna del proveedor"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de stock"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order_line__qty_received_method__stock_moves
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_line_view_form_inherit
msgid "Stock Moves"
msgstr "Movimientos de stock"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Reporte de reabastecimiento de existencias"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Regla de inventario"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "Capa de valoración de existencias"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Reporte de regla de inventario"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Información de reabastecimiento del proveedor de inventario"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "Opción de reabastecimiento de almacén de existencias"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__supplier_id
msgid "Supplier"
msgstr "Proveedor"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Lista de precios del proveedor"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_ids
msgid "Supplierinfo"
msgstr "Información del proveedor"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr "Campo técnico utilizado para mostrar la dirección de envío directo"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock.py:0
msgid "The following replenishment order has been generated"
msgstr "Se generó la siguiente orden de reabastecimiento"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund."
msgstr ""
"Las cantidades en su orden de compra son menos que las facturadas. Solicite "
"un reembolso. "

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"The warehouse of operation type (%(operation_type)s) is inconsistent with "
"location (%(location)s) of reordering rule (%(reordering_rule)s) for product"
" %(product)s. Change the operation type or cancel the request for quotation."
msgstr ""
"El almacén del tipo de operación (%(operation_type)s) es incompatible con la"
" ubicación (%(location)s) de la regla de reordenamiento "
"(%(reordering_rule)s) para el producto %(product)s. Cambie el tipo de "
"operación o cancele la solicitud de cotización."

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid ""
"There is no matching vendor price to generate the purchase order for product"
" %s (no vendor defined, minimum quantity not reached, dates not valid, ...)."
" Go on the product form and complete the list of vendors."
msgstr ""
"No existe un precio de proveedor coincidente para generar la orden de compra"
" del producto %s (no definió un proveedor, no se alcanzó la cantidad mínima,"
" las fechas no son válidas, ...). Vaya al formulario del producto y complete"
" la lista de proveedores."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase_stock.field_product_template__property_account_creditor_price_difference
msgid ""
"This account is used in automated inventory valuation to record the price "
"difference between a purchase order and its related vendor bill when "
"validating this vendor bill."
msgstr ""
"Esta cuenta se utiliza en la valoración de inventario automatizada para "
"registrar la diferencia de precio entre una orden de compra y su factura de "
"proveedor relacionada al validar esta factura de proveedor."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_category__property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""
"Esta cuenta se utilizará para valorar la diferencia de precios entre el "
"precio de compra y precio contabilizado."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"Esto agrega una ruta de triangulación aplicable a los productos para "
"solicitar que sus proveedores los entreguen a sus clientes. Un producto para"
" triangulación generará una solicitud de cotización de compra al confirmar "
"la orden de venta. A esto se le llama flujo bajo demanda. La dirección de "
"entrega solicitada será la dirección de entrega del cliente, no su almacén."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr "Esto determinará el tipo de operación del envío entrante"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid ""
"Those dates couldn’t be modified accordingly on the receipt %s which had "
"already been validated."
msgstr ""
"No se pudieron modificar las fechas de acuerdo con el recibo %s que ya se "
"había validado."

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "Those dates have been updated accordingly on the receipt %s."
msgstr "Las fechas se actualizaron de acuerdo con el recibo %s."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_total
msgid "Total Quantity"
msgstr "Cantidad total"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_picking
msgid "Transfer"
msgstr "Trasladar"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid ""
"Unable to cancel purchase order(s): %s since they have receipts that are "
"already done."
msgstr ""
"No puede cancelar las órdenes de compra %s porque ya se han completado "
"algunas recepciones."

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Validate the receipt of all ordered products."
msgstr "Validar la entrega de todos los productos ordenados"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_replenish__supplier_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenish_mixin__supplier_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_warehouse_orderpoint_tree_editable_inherited_mrp
msgid "Vendor"
msgstr "Proveedor"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_vendor_delay_report
msgid "Vendor Delay Report"
msgstr "Reporte de demora del proveedor"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "Vendor Lead Time"
msgstr "Plazo de entrega del proveedor"

#. module: purchase_stock
#: model_terms:ir.actions.act_window,help:purchase_stock.action_purchase_vendor_delay_report
msgid "Vendor On-time Delivery analysis"
msgstr "Análisis de entregas a tiempo del proveedor"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_stock_replenishment_info_stock_purchase_inherit
msgid "Vendors"
msgstr "Proveedores"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse_orderpoint__purchase_visibility_days
msgid "Visibility Days applied on the purchase routes."
msgstr "Días de visibilidad aplicados a las rutas de compra."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__picking_type_id
msgid "Warehouse"
msgstr "Almacén"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "Los productos se podrán enviar a este almacén cuando se compren"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%s</b>, <br/> a request for quotation is "
"created to fulfill the need.<br/>Note: This rule will be used in combination"
" with the rules<br/>of the reception route(s)"
msgstr ""
"Cuando se necesiten productos en <b>%s</b>, <br/> se generará una solicitud "
"de cotización para satisfacer la necesidad.<br/>Nota: esta regla se "
"utilizará en combinación con las reglas<br/>de las rutas de recepción"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"You cannot decrease the ordered quantity below the received quantity.\n"
"Create a return first."
msgstr ""
"No puede disminuir la cantidad ordenada por debajo de la cantidad recibida.\n"
"Cree una devolución primero."

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "You must set a Vendor Location for this partner %s"
msgstr "Debe asignar una ubicación para este contacto %s"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "días"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "of"
msgstr "de"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "ordered instead of"
msgstr "ordenado en lugar de"
