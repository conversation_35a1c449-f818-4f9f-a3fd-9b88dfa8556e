# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_stock
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Tiffany Chang, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "% On-Time Delivery"
msgstr "٪ تحویل به موقع"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d روز"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_text\">On-time Rate</span>"
msgstr "<span class=\"o_stat_text\">نرخ سروقت</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">خریدها</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_value\">%</span>"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "<span invisible=\"on_time_rate &gt;= 0\">No On-time Delivery Data</span>"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "<span> days</span>"
msgstr "<span> روز</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_view_kanban_catalog_purchase_only
msgid "<span>Forecasted: </span>"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>اینکوترم:</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>نشانی ارسال:</strong>"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_rule__action
msgid "Action"
msgstr "عمل"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__effective_date
msgid "Arrival"
msgstr "ورودی ها"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock.py:0
#: model:ir.model.fields.selection,name:purchase_stock.selection__stock_rule__action__buy
#: model:stock.route,name:purchase_stock.route_warehouse0_buy
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_stock_rule
msgid "Buy"
msgstr "خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_pull_id
msgid "Buy rule"
msgstr "قاعده خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "Buy to Resupply"
msgstr "خرید برای تامین مجدد"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__effective_date
msgid "Completion date of the first receipt order."
msgstr "تاریخ تکمیل اولین سفارش دریافتی."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "Corresponding receipt not found."
msgstr "رسید مربوطه یافت نشد."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__created_purchase_line_ids
msgid "Created Purchase Order Lines"
msgstr ""

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_move.py:0
msgid "Currency exchange rate difference"
msgstr "اختلاف نرخ تبدیل ارز"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__product_description_variants
msgid "Custom Description"
msgstr "توضیحات سفارشی"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__days_to_arrive
msgid "Days To Arrive"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Days needed to confirm a PO"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,help:purchase_stock.field_res_config_settings__days_to_purchase
msgid "Days needed to confirm a PO, define when a PO should be validated"
msgstr ""
"روزهای مورد نیاز برای تایید یک PO، تعیین کنید که یک PO باید چه زمانی اعتبار "
"سنجی شود"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__days_to_purchase
msgid "Days to Purchase"
msgstr "روز برای خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__delay_pass
msgid "Delay Pass"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_type_id
msgid "Deliver To"
msgstr "تحویل به"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,help:purchase_stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"بسته به ماژول های نصب شده، این به شما امکان می دهد مسیر محصول را مشخص کنید: "
"آیا خریداری می شود، تولید می شود، بر اساس سفارش دوباره پر می شود و غیره."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "نوع مکان مقصد"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_dest_ids
msgid "Downstream moves alt"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "آدرس دراپشیپینگ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "دراپشیپینگ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__date
msgid "Effective Date"
msgstr "تاریخ اجرا"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "Effective Date Last Year"
msgstr "تاریخ لازم الاجرا شدن در سال گذشته"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__days_to_arrival
msgid "Effective Days To Arrival"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "استثنا(هایی) در سفارش(های) خرید رخ داده است:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s):"
msgstr "استثنا(ها):"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Forecast Report"
msgstr "گزارش پیش بینی"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__forecasted_issue
msgid "Forecasted Issue"
msgstr "مسئله پیش بینی شده"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__full
msgid "Fully Received"
msgstr "کاملا دریافت شده"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Generate the draft vendor bill."
msgstr "ایجاد پیش‌نویس صورتحساب فروشنده"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Go back to the purchase order to generate the vendor bill."
msgstr "برای ایجاد صورتحساب فروشنده به سفارش خرید برگردید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__id
msgid "ID"
msgstr "شناسه"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoming_picking_count
msgid "Incoming Shipment count"
msgstr "تعداد محموله های دریافتی"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Incoming Shipments"
msgstr "محموله ی در حال آمدن"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoterm_location
msgid "Incoterm Location"
msgstr "محل اصطلاحات بازرگانی"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__is_shipped
msgid "Is Shipped"
msgstr "ارسال شده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__is_installed_sale
msgid "Is the Sale Module Installed"
msgstr "آیا ماجول فروش نصب شده است"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move
msgid "Journal Entry"
msgstr "سند دفترروزنامه‌"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move_line
msgid "Journal Item"
msgstr "آیتم دفترروزنامه"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__last_purchase_date
msgid "Last Purchase"
msgstr "آخرین خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_procurement_group__purchase_line_ids
msgid "Linked Purchase Order Lines"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__location_final_id
msgid "Location from procurement"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr "لجستیک"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_lot
msgid "Lot/Serial"
msgstr "سری/سریال"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Manual actions may be needed."
msgstr "ممکن است اقدامات دستی لازم باشد."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"حاشیه خطا برای زمان تحویل فروشنده. وقتی سیستم سفارش‌های خرید را برای "
"سفارش‌دهی مجدد محصولات ایجاد می‌کند، برای مقابله با تأخیرهای غیرمنتظره "
"فروشنده، آن‌ها چند روز زودتر برنامه‌ریزی می‌شوند."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "قاعده حداقل انبارداری"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Move forward expected request creation date by"
msgstr "تاریخ ایجاد درخواست مورد انتظار را به جلو ببرید"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Next transfer(s) impacted:"
msgstr "انتقال(های) بعدی تحت تاثیر قرار گرفته:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "No data yet"
msgstr "هنوز داده ای وجود ندارد"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_help_message_template
msgid "No receipt yet! Automate them with purchase orders."
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__pending
msgid "Not Received"
msgstr "دریافت نشد"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_move.py:0
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr "Odoo قادر به تولید ورودی های آنگلوساکسون نیست. ارزش کل %s صفر است."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_view_graph
msgid "On-Time Delivery"
msgstr "تحویل به موقع"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__on_time_rate
msgid "On-Time Delivery Rate"
msgstr "نرخ تحویل به موقع"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_on_time
msgid "On-Time Quantity"
msgstr "تعداد به موقع"

#. module: purchase_stock
#: model:ir.actions.act_window,name:purchase_stock.action_purchase_vendor_delay_report
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "On-time Delivery"
msgstr "تحویل به موقع"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "On-time Rate"
msgstr "نرخ به موقع"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__orderpoint_id
msgid "Orderpoint"
msgstr "نقطه سفارش"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_users__on_time_rate
msgid ""
"Over the past x days; the number of products received on time divided by the"
" number of ordered products.x is either the System Parameter "
"purchase_stock.on_time_delivery_days or the default 365"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__purchase_order_line_ids
msgid "PO Lines"
msgstr "سطر ارجاع خرید"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order__receipt_status__partial
msgid "Partially Received"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_category__property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "قیمت حساب های متفاوت"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Process all the receipt quantities."
msgstr "تمام مقادیر رسید را پردازش کنید."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_procurement_group
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__group_id
msgid "Procurement Group"
msgstr "گروه تدارکات"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__group_id
msgid "Procurement group that generated this line"
msgstr ""

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_template
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__product_id
msgid "Product"
msgstr "محصول"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_category
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__category_id
msgid "Product Category"
msgstr "دسته بندی محصول"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_replenish
msgid "Product Replenish"
msgstr "تأمین مجدد محصول"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__product_supplier_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.warehouse_orderpoint_search_inherit
msgid "Product Supplier"
msgstr "تامین کننده محصول"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_product
msgid "Product Variant"
msgstr "گونه محصول"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__propagate_cancel
msgid "Propagate cancellation"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__purchase_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__purchase_line_ids
msgid "Purchase Lines"
msgstr "سطرهای خرید"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order
msgid "Purchase Order"
msgstr "سفارش خرید"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__purchase_line_id
msgid "Purchase Order Line"
msgstr "سطر سفارش خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_lot__purchase_order_ids
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__purchase_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_help_message_template
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "Purchase Orders"
msgstr "سفارشات خرید"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_report
msgid "Purchase Report"
msgstr "گزارش خرید"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "Purchase Security Lead Time"
msgstr "زمان تحویل مطمئن خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__purchase_visibility_days
msgid "Purchase Visibility Days"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_lot__purchase_order_count
msgid "Purchase order count"
msgstr "تعداد سفارش خرید"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"اگر می خواهید مستقیماً از فروشنده به مشتری تحویل دهید، یک آدرس قرار دهید. در"
" غیر این صورت خالی نگه دارید تا به شرکت خودتان تحویل دهید."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receipt"
msgstr "رسید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__receipt_status
msgid "Receipt Status"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receive Products"
msgstr "دریافت محصولات"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Receive the ordered products."
msgstr "محصولات سفارش داده شده را دریافت کنید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "روش تعداد دریافت شده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_ids
msgid "Receptions"
msgstr "رسیدها"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__receipt_status
msgid ""
"Red: Late\n"
"            Orange: To process today\n"
"            Green: On time"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr "از فروشندگان بخواهید که به مشتری‌ها تحویل دهند"

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/purchase_stock_forecasted/forecasted_details.xml:0
msgid "Requests for quotation"
msgstr "درخواست استعلام قیمت"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_ids
msgid "Reservation"
msgstr "رزرو کردن"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "برداشت بازگشت"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__route_ids
msgid "Routes"
msgstr "مسیرها"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Schedule request for quotations earlier to avoid delays"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_supplierinfo_replenishment_tree_view
msgid "Set as Supplier"
msgstr "تنظیم به عنوان تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__show_set_supplier_button
msgid "Show Set Supplier Button"
msgstr "نمایش دکمه تنظیم تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_replenish__show_vendor
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenish_mixin__show_vendor
msgid "Show Vendor"
msgstr "نمایش تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__show_supplier
msgid "Show supplier column"
msgstr "نمایش ستون تامین کننده"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_move
msgid "Stock Move"
msgstr "انتقال موجودی"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order_line__qty_received_method__stock_moves
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_line_view_form_inherit
msgid "Stock Moves"
msgstr "انتقالهای موجودی"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "گزارش جایگزینی انبار"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_rule
msgid "Stock Rule"
msgstr "قانون موجودی انبار"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "لایه ارزش گذاری موجودی"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "گزارش قانون موجودی انبار"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "اطلاعات تکمیلی تامین کننده موجودی"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "گزینه تأمین مجدد انبار کالا"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__supplier_id
msgid "Supplier"
msgstr ""

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "لیست قیمت تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_ids
msgid "Supplierinfo"
msgstr "اطلاعات تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr "فیلد فنی مورد استفاده برای نمایش آدرس دراپشیپ"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock.py:0
msgid "The following replenishment order has been generated"
msgstr "سفارش تأمین زیر ایجاد شده است"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund."
msgstr ""
"مقادیر در سفارش خرید شما نشان دهنده کمتر از صورتحساب است. شما باید درخواست "
"بازپرداخت کنید."

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"The warehouse of operation type (%(operation_type)s) is inconsistent with "
"location (%(location)s) of reordering rule (%(reordering_rule)s) for product"
" %(product)s. Change the operation type or cancel the request for quotation."
msgstr ""

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid ""
"There is no matching vendor price to generate the purchase order for product"
" %s (no vendor defined, minimum quantity not reached, dates not valid, ...)."
" Go on the product form and complete the list of vendors."
msgstr ""
"هیچ قیمت فروشنده منطبقی برای ایجاد سفارش خرید برای محصول %s وجود ندارد "
"(فروشنده ای تعریف نشده است، به حداقل مقدار رسیده نیست، تاریخ ها معتبر "
"نیستند، ...). به فرم محصول بروید و لیست فروشندگان را تکمیل کنید."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase_stock.field_product_template__property_account_creditor_price_difference
msgid ""
"This account is used in automated inventory valuation to record the price "
"difference between a purchase order and its related vendor bill when "
"validating this vendor bill."
msgstr ""
"این حساب در ارزیابی خودکار موجودی برای ثبت تفاوت قیمت بین یک سفارش خرید و "
"صورت‌حساب فروشنده مربوطه آن هنگام تایید این صورتحساب فروشنده استفاده می‌شود."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_category__property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""
"این حساب برای ارزش گذاری تفاوت قیمت بین قیمت خرید و هزینه حسابداری استفاده "
"می شود."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"این یک مسیر دراپشیپینگ را اضافه می‌کند تا روی محصولات اعمال شود تا از "
"فروشندگان درخواست کنید که به مشتریان شما تحویل دهند. محصولی که به داپشیپ "
"ارسال می‌شود، پس از تأیید سفارش فروش، درخواست خرید برای استعلام قیمت ایجاد "
"می‌کند. این یک جریان بر اساس تقاضا است. آدرس تحویل درخواستی آدرس تحویل مشتری"
" خواهد بود و نه انبار شما."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr "این نوع عملیات محموله ورودی را تعیین می کند"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid ""
"Those dates couldn’t be modified accordingly on the receipt %s which had "
"already been validated."
msgstr ""
"این تاریخ‌ها را نمی‌توان بر اساس رسید %s که قبلاً تأیید شده بود تغییر داد."

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "Those dates have been updated accordingly on the receipt %s."
msgstr "این تاریخ ها بر این اساس در رسید %s بروز شده اند."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_total
msgid "Total Quantity"
msgstr "تعداد کل"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_picking
msgid "Transfer"
msgstr "انتقال"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid ""
"Unable to cancel purchase order(s): %s since they have receipts that are "
"already done."
msgstr ""

#. module: purchase_stock
#. odoo-javascript
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
msgid "Validate the receipt of all ordered products."
msgstr "رسید تمام محصولات سفارش داده شده را تایید کنید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_replenish__supplier_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenish_mixin__supplier_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_warehouse_orderpoint_tree_editable_inherited_mrp
msgid "Vendor"
msgstr "فروشنده"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_vendor_delay_report
msgid "Vendor Delay Report"
msgstr "گزارش تاخیر فروشنده"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid "Vendor Lead Time"
msgstr "زمان تحویل فروشنده"

#. module: purchase_stock
#: model_terms:ir.actions.act_window,help:purchase_stock.action_purchase_vendor_delay_report
msgid "Vendor On-time Delivery analysis"
msgstr "تجزیه و تحلیل تحویل به موقع فروشنده"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_stock_replenishment_info_stock_purchase_inherit
msgid "Vendors"
msgstr "فروشندگان"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse_orderpoint__purchase_visibility_days
msgid "Visibility Days applied on the purchase routes."
msgstr ""

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__picking_type_id
msgid "Warehouse"
msgstr "انبار"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "وقتی محصولات خرید شدند، آنها می‌توانند به این انبار تحویل شوند"

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%s</b>, <br/> a request for quotation is "
"created to fulfill the need.<br/>Note: This rule will be used in combination"
" with the rules<br/>of the reception route(s)"
msgstr ""

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order_line.py:0
msgid ""
"You cannot decrease the ordered quantity below the received quantity.\n"
"Create a return first."
msgstr ""
"شما نمی توانید مقدار سفارش داده شده را کمتر از مقدار دریافتی کاهش دهید.\n"
"ابتدا یک بازگشت ایجاد کنید."

#. module: purchase_stock
#. odoo-python
#: code:addons/purchase_stock/models/purchase_order.py:0
msgid "You must set a Vendor Location for this partner %s"
msgstr "برای این همکار باید یک مکان فروشنده تعریف کنید %s"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "روز"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "of"
msgstr "از"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "ordered instead of"
msgstr "سفارش داده شده به جای"
