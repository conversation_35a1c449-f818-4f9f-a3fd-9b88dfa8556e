# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * calendar_sms
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Georgian (https://www.transifex.com/odoo/teams/41243/ka/)\n"
"Language: ka\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_event
msgid "Calendar Event"
msgstr ""

#. module: calendar_sms
#: model:sms.template,name:calendar_sms.sms_template_data_calendar_reminder
msgid "Calendar Event: Reminder"
msgstr ""

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_alarm
msgid "Event Alarm"
msgstr ""

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr ""

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_event.py:0
msgid "Event reminder: %(name)s, %(time)s."
msgstr ""

#. module: calendar_sms
#: model:sms.template,body:calendar_sms.sms_template_data_calendar_reminder
msgid "Event reminder: {{ object.name }}, {{ object.get_display_time_tz(object.partner_id.tz) }}"
msgstr ""

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_alarm.py:0
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__sms_notify_responsible
msgid "Notify Responsible"
msgstr ""

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_form_inherited
msgid "SMS"
msgstr ""

#. module: calendar_sms
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__sms_template_id
msgid "SMS Template"
msgstr ""

#. module: calendar_sms
#: model:ir.model.fields.selection,name:calendar_sms.selection__calendar_alarm__alarm_type__sms
msgid "SMS Text Message"
msgstr ""

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_tree_inherited
msgid "Send SMS"
msgstr ""

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_event.py:0
msgid "Send SMS Text Message"
msgstr ""

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_form_inherited
msgid "Send SMS to attendees"
msgstr ""

#. module: calendar_sms
#: model:ir.model.fields,help:calendar_sms.field_calendar_alarm__sms_template_id
msgid "Template used to render SMS reminder content."
msgstr ""

#. module: calendar_sms
#. odoo-python
#: code:addons/calendar_sms/models/calendar_event.py:0
msgid "There are no attendees on these events"
msgstr ""

#. module: calendar_sms
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__alarm_type
msgid "Type"
msgstr ""
