<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- better support for long company name -->
        <template id="report_statement_internal_layout" inherit_id="web.internal_layout" primary="True">
            <xpath expr="//div[hasclass('col-3')]" position="attributes">
                <attribute name="class">col-4</attribute>
            </xpath>
            <xpath expr="//div[hasclass('col-2') and hasclass('offset-2') and hasclass('text-center')]" position="attributes">
                <attribute name="class">col-4 text-center</attribute>
            </xpath>
            <xpath expr="//div[hasclass('col-2') and hasclass('offset-3') and hasclass('text-end')]" position="attributes">
                <attribute name="class">col-2 offset-2 text-end</attribute>
            </xpath>
        </template>

        <template id="report_statement">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="account.report_statement_internal_layout">
                        <div class="page">
                        <div class="oe_structure"></div>
                            <div class="border border-top-0 border-end-0 border-start-0 border-info">
                                <div class="row">
                                    <div class="col-12">
                                        <h4 class="text-center w-100">
                                            <strong>
                                                <span t-if="o.journal_id.type == 'bank'">Bank Statement</span>
                                                <span t-else="">Cash Statement</span>
                                            </strong>
                                        </h4>
                                    </div>
                                </div>
                            </div>

                            <div class="oe_structure"></div>
                            <div class="pt-2 pb-2">
                                <div class="row">
                                    <div class="col-7">
                                        <h5>
                                            <strong>
                                                <span t-out="o.journal_id.display_name">Journal Name</span>
                                                <span t-if="o.journal_id.bank_account_id">
                                                    - <span t-out="o.journal_id.bank_account_id.display_name">Bank Account Name</span>
                                                </span>
                                                <span t-if="o.journal_id.code">
                                                    - <span t-out="o.journal_id.code">12345</span>
                                                </span>
                                            </strong>
                                        </h5>
                                    </div>
                                    <div class="col-5" style="text-align: end;">
                                        <h5>
                                            <strong>
                                                <span t-if="o.name">
                                                    <span t-field="o.name">Statement Name</span>
                                                - </span>
                                                <span t-field="o.date">2023-08-15</span>
                                            </strong>
                                        </h5>
                                    </div>
                                </div>
                            </div>

                            <div class="oe_structure"></div>
                            <table class="table table-lg m-0 table-borderless border border-end-0 border-start-0 border-info">
                                <tbody>
                                    <tr>
                                        <td class="pt-2 pb-2 ps-0 pe-0">
                                            <table class="table table-borderless m-0">
                                                <tr>
                                                    <td class="p-0 w-25">
                                                        <strong>Starting Balance</strong>
                                                    </td>
                                                    <td class="p-0">
                                                        <strong>
                                                            <span t-out="o.line_ids and o.line_ids.sorted(lambda line: line.date)[0].date" t-options='{"widget": "date"}'>2023-08-11</span>
                                                        </strong>
                                                    </td>
                                                    <td class="text-end p-0">
                                                        <strong>
                                                            <span t-field="o.balance_start">1000.0</span>
                                                        </strong>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="p-0 w-25">
                                                        <strong>Ending Balance</strong>
                                                    </td>
                                                    <td class="p-0">
                                                        <strong>
                                                            <span t-out="o.line_ids and o.line_ids.sorted(lambda line: line.date)[-1].date" t-options='{"widget": "date"}'>2023-08-31</span>
                                                        </strong>
                                                    </td>
                                                    <td class="text-end p-0">
                                                        <strong>
                                                            <span t-field="o.balance_end_real">1500.0</span>
                                                        </strong>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="oe_structure"></div>
                            <table class="table table-borderless m-0">
                                <tbody>
                                    <tr t-foreach="o.line_ids" t-as="line">
                                        <td class="w-25 py-1">
                                            <span class="d-block fw-bold" t-field="line.date">2023-08-11</span>
                                        </td>
                                        <td class="py-1">
                                            <span class="d-block fw-bold" t-if="line.partner_id">
                                                <span t-out="line.partner_id.name">Marc Demo</span> <span t-if="line.partner_bank_id and line.partner_bank_id.partner_id != line.partner_id">(<span t-out="line.partner_bank_id.partner_id.name">Bank of odoo</span>)</span>
                                            </span>
                                            <span class="d-block" t-if="line.partner_bank_id or line.account_number" t-out="line.account_number or line.partner_bank_id.acc_number">************</span>
                                            <span class="d-block" t-if="line.payment_ref" t-field="line.payment_ref">Payment Reference</span>
                                        </td>
                                        <td class="text-end py-1">
                                            <span t-att-class="'d-block fw-bold' + (' text-danger' if line.amount &lt; 0 else '')" t-field="line.amount">100.0</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="oe_structure"></div>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>
