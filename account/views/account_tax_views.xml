<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_tax_tree" model="ir.ui.view">
            <field name="name">account.tax.list</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <list string="Account Tax" decoration-muted="not active">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="description" optional="show"/>
                    <field name="type_tax_use"/>
                    <field name="tax_scope"/>
                    <field name="invoice_label"/>
                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                    <field name="country_id" optional="hide"/>
                    <field name="active" widget="boolean_toggle"/>
                </list>
            </field>
        </record>

        <record id="view_onboarding_tax_tree" model="ir.ui.view">
            <field name="name">account.onboarding.tax.list</field>
            <field name="model">account.tax</field>
            <field name="inherit_id" ref="account.view_tax_tree" />
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//list" position="attributes">
                    <attribute name="default_order">active desc, type_tax_use desc, amount desc, sequence</attribute>
                </xpath>
            </field>
        </record>

        <record id="tax_repartition_line_tree" model="ir.ui.view">
            <field name="name">account.tax.repartition.line.list</field>
            <field name="model">account.tax.repartition.line</field>
            <field name="arch" type="xml">
                <list editable="bottom" create="1" delete="1">
                    <field name="sequence" widget="handle"/>
                    <field name="factor_percent" invisible="repartition_type == 'base'"/>
                    <field name="repartition_type"/>
                    <field name="account_id" invisible="repartition_type == 'base'" options="{'no_create': True}"/>
                    <field name="tag_ids"
                           widget="many2many_tags"
                           options="{'no_create': True}"
                           domain="tag_ids_domain"/>
                    <field name="use_in_tax_closing"
                           optional="hidden"
                           invisible="repartition_type == 'base'"/>
                    <field name="company_id" column_invisible="True"/>
                    <field name="tag_ids_domain" column_invisible="True"/>
                </list>
            </field>
        </record>

        <record id="account_tax_view_tree" model="ir.ui.view">
            <field name="name">account.invoice.line.tax.search</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <list string="Account Tax">
                    <field name="display_name" string="Name"/>
                    <field name="tax_scope"/>
                    <field name="description"/>
                </list>
            </field>
        </record>

        <record id="view_tax_kanban" model="ir.ui.view">
            <field name="name">account.tax.kanban</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <templates>
                        <t t-name="card">
                            <div class="row mb4">
                                <div class="col-6">
                                    <field class="fw-bolder" name="name"/>
                                </div>
                                <div class="col-6 text-end">
                                    <field class="badge rounded-pill" name="type_tax_use"/>
                                    <field class="badge rounded-pill" name="tax_scope"/>
                                </div>
                            </div>
                            <field class="text-muted" name="description"/>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_account_tax_search" model="ir.ui.view">
            <field name="name">account.tax.search</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <search string="Search Taxes">
                    <field name="name_searchable" string="Name"/>
                    <field name="description"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <filter string="Sale" name="sale" domain="[('type_tax_use','=','sale')]" />
                    <filter string="Purchase" name="purchase" domain="[('type_tax_use','=','purchase')]" />
                    <separator/>
                    <filter string="Services" name="service" domain="[('tax_scope','=','service')]" />
                    <filter string="Goods" name="goods" domain="[('tax_scope','=','consu')]" />
                    <separator/>
                    <filter name="active" string="Active" domain="[('active','=',True)]" help="Show active taxes"/>
                    <filter name="inactive" string="Inactive" domain="[('active','=',False)]" help="Show inactive taxes"/>
                    <group string="Group By">
                        <filter string="Company" name="company" domain="[]" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Tax Type" name="taxapp" domain="[]" context="{'group_by':'type_tax_use'}"/>
                        <filter string="Tax Scope" name="taxapp" domain="[]" context="{'group_by':'tax_scope'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="account_tax_view_search" model="ir.ui.view">
            <field name="name">account.tax.search.filters</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <search string="Search Taxes">
                    <field name="name" filter_domain="['|', '|', ('name', 'ilike', self), ('description', 'ilike', self), ('invoice_label', 'ilike', self)]" string="Tax"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </search>
            </field>
        </record>

        <record id="view_tax_form" model="ir.ui.view">
            <field name="name">account.tax.form</field>
            <field name="model">account.tax</field>
            <field name="arch" type="xml">
                <form string="Account Tax">
                    <field name="company_id" invisible="1"/>
                    <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="amount_type"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                        <group>
                            <field name="is_used" invisible="1"/>
                            <field name="type_tax_use"/>
                            <field name="tax_scope"/>
                            <label for="amount" invisible="amount_type not in ('fixed', 'percent', 'division')"/>
                            <div invisible="amount_type not in ('fixed', 'percent', 'division')">
                                <field name="amount" class="oe_inline" nolabel="1"/>
                                <span class="o_form_label oe_inline" invisible="amount_type == 'fixed'">%</span>
                            </div>
                        </group>
                    </group>
                    <notebook>
                        <page string="Definition" name="definition">
                            <div invisible="amount_type == 'group'">
                                <field name="country_code" invisible="1"/>
                                <group string="Distribution for Invoices" class="mw-100">
                                    <field name="invoice_repartition_line_ids" nolabel="1"/>
                                </group>
                                <group string="Distribution for Refunds" class="mw-100">
                                    <field name="refund_repartition_line_ids" nolabel="1"/>
                                </group>
                            </div>
                            <field name="children_tax_ids" invisible="amount_type != 'group' or type_tax_use == 'none'" domain="[('type_tax_use','in',('none',type_tax_use)), ('amount_type','!=','group')]">
                                <list string="Children Taxes">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="amount_type"/>
                                    <field name="amount"/>
                                </list>
                            </field>
                        </page>
                        <page string="Advanced Options" name="advanced_options">
                            <group>
                                <group>
                                    <field name="invoice_label" invisible="amount_type == 'group'"/>
                                    <field name="description"/>
                                    <field name="tax_group_id" invisible="amount_type == 'group'" required="amount_type != 'group'"/>
                                    <field name="analytic" invisible="amount_type == 'group'" groups="analytic.group_analytic_accounting" />
                                    <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                                    <field name="country_id" required="True"/>
                                    <field name="invoice_legal_notes"/>
                                </group>
                                <group name="advanced_booleans">
                                    <field name="price_include" invisible="1"/>
                                    <field name="price_include_override"
                                           invisible="amount_type == 'group'"
                                           placeholder="Default"
                                    />
                                    <field name="include_base_amount" invisible="amount_type == 'group'"/>
                                    <field name="is_base_affected"
                                           invisible="amount_type == 'group' or price_include"
                                           groups="base.group_no_one"/>
                                    <field name="hide_tax_exigibility" invisible="1"/>
                                    <field name="tax_exigibility" widget="radio" invisible="amount_type == 'group' or not hide_tax_exigibility"/>
                                    <field name="cash_basis_transition_account_id" options="{'no_create': True}" invisible="tax_exigibility == 'on_invoice'" required="tax_exigibility == 'on_payment'" groups="account.group_account_readonly"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
                </form>
              </field>
        </record>

        <record id="action_tax_form" model="ir.actions.act_window">
            <field name="name">Taxes</field>
            <field name="res_model">account.tax</field>
            <field name="view_mode">list,kanban,form</field>
            <field name="context">{'search_default_sale': True, 'search_default_purchase': True, 'active_test': False}</field>
            <field name="view_id" ref="view_tax_tree"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new tax
              </p>
            </field>
        </record>

        <record id="account_tax_group_view_search" model="ir.ui.view">
            <field name="name">account.tax.group.search.filters</field>
            <field name="model">account.tax.group</field>
            <field name="arch" type="xml">
                <search string="Search Group">
                    <field name="name"/>
                    <field name="country_id"/>
                    <group string="Group By">
                        <filter string="Country" name="group_by_country" domain="[]" context="{'group_by': 'country_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_tax_group_tree" model="ir.ui.view">
            <field name="name">account.tax.group.list</field>
            <field name="model">account.tax.group</field>
            <field name="arch" type="xml">
                <list string="Account Tax Group" editable="bottom" create="false" open_form_view="True">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="country_id"/>
                    <field name="company_id" column_invisible="True"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="country_code" column_invisible="True"/>

                    <field name="tax_payable_account_id"/>
                    <field name="tax_receivable_account_id"/>
                    <field name="advance_tax_payment_account_id"/>

                    <field name="preceding_subtotal" optional="hide"/>
                </list>
            </field>
        </record>

        <record id="view_tax_group_form" model="ir.ui.view">
            <field name="name">account.tax.group.form</field>
            <field name="model">account.tax.group</field>
            <field name="arch" type="xml">
                <form string="Account Tax Group">
                    <field name="company_id" invisible="1"/>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="country_id"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="sequence"/>
                                <field name="pos_receipt_label" string="Label on PoS Receipts" groups="base.group_no_one"/>
                            </group>
                            <group>
                                <field name="tax_payable_account_id" domain="[('company_ids', '=', company_id)]"/>
                                <field name="tax_receivable_account_id" domain="[('company_ids', '=', company_id)]"/>
                                <field name="advance_tax_payment_account_id" domain="[('company_ids', '=', company_id)]"/>
                                <field name="preceding_subtotal"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_tax_group" model="ir.actions.act_window">
            <field name="name">Tax Groups</field>
            <field name="res_model">account.tax.group</field>
            <field name="view_mode">list,form</field>
            <field name="view_id" ref="view_tax_group_tree"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new tax group
              </p>
            </field>
        </record>

    </data>
</odoo>
