<odoo>
    <record id="account_journal_dashboard_kanban_view" model="ir.ui.view">
        <field name="name">account.journal.dashboard.kanban</field>
        <field name="model">account.journal</field>
        <field name="arch" type="xml">
            <kanban create="false" can_open="0" highlight_color="color" class="o_account_kanban" js_class="account_dashboard_kanban">
                <field name="id"/>
                <field name="type"/>
                <field name="color"/>
                <field name="kanban_dashboard"/>
                <field name="has_entries"/>
                <field name="has_posted_entries"/>
                <field name="activity_ids"/>
                <field name="activity_state"/>
                <field name="alias_domain_id"/>
                <field name="bank_account_id"/>
                <templates>
                    <t t-name="card">
                        <t t-value="JSON.parse(record.kanban_dashboard.raw_value)" t-set="dashboard"/>
                        <t t-value="record.type.raw_value" t-set="journal_type"/>
                        <t t-value="!record.has_entries.raw_value" t-set="journal_is_empty"/>
                        <t t-set="bank_unconfigured" t-value="(journal_type == 'bank' or journal_type == 'credit')
                                                                and ['undefined', 'online_sync'].includes(dashboard.bank_statements_source)
                                                                and journal_is_empty"/>
                        <t t-call="JournalTop"/>
                        <div t-att-class="'mt-3 p-0 container-fluid' + (dashboard.is_sample_data ? ' o_sample_data' : '')">
                            <div class="row">
                                <t t-if="journal_type == 'bank' || journal_type == 'credit'" t-call="JournalBodyBank"/>
                                <t t-if="journal_type == 'cash'" t-call="JournalBodyBankCash"/>
                                <t t-if="journal_type == 'sale' || journal_type == 'purchase'" t-call="JournalBodySalePurchase"/>
                                <t t-if="journal_type == 'general'" t-call="JournalMiscelaneous"/>
                            </div>
                            <div class="row mt-auto">
                                <t t-if="['bank', 'cash', 'credit', 'sale'].includes(journal_type)
                                            and !bank_unconfigured
                                            or (journal_type == 'purchase' and !journal_is_empty)"
                                    t-call="JournalBodyGraph"/>
                            </div>
                        </div>
                    </t>

                    <t t-name="JournalTop">
                        <span class="d-flex d-flew-row align-items-baseline">
                            <a class="fs-4 fw-bold" type="object" name="open_action"><field name="name"/></a>
                            <t t-if="dashboard.show_company" groups="base.group_multi_company" >
                                <span class="small fw-bold ms-1"> - <field name="company_id"/></span>
                            </t>
                        </span>
                        <div t-att-title="dashboard.title" t-if="journal_type == 'purchase' &amp;&amp; record.alias_domain_id.raw_value &amp;&amp; !journal_is_empty">
                            <field name="alias_id"/>
                        </div>
                        <t t-if="journal_is_empty" name="empty_journal_helper">
                            <span class="text-muted" t-if="journal_type == 'sale'">Get Paid online. Send electronic invoices.</span>
                            <span class="text-muted" t-if="journal_type == 'purchase'">Let artificial intelligence scan your bill. Pay easily.</span>
                            <span class="text-muted" t-if="journal_type == 'bank'">Connect your bank. Match invoices automatically.</span>
                        </t>
                    </t>

                    <t t-name="menu">
                        <t t-value="JSON.parse(record.kanban_dashboard.raw_value)" t-set="dashboard"/>
                        <t t-value="record.type.raw_value" t-set="journal_type"/>
                        <div class="container">
                            <!-- For bank, cash and credit -->
                            <div t-if="journal_type == 'bank' || journal_type == 'cash' || journal_type == 'credit'" class="row" id="bank_and_cash_container">
                                <div class="col-4">
                                    <h5 id="card_action_view_menus" class="o_kanban_card_manage_title">
                                        <span role="separator">View</span>
                                    </h5>
                                    <div id="action_card_statements">
                                        <a t-if="journal_type == 'bank'" role="menuitem" type="object" name="open_action_with_context" context="{'action_name': 'action_bank_statement_tree', 'search_default_journal': True}">Statements</a>
                                        <a t-if="journal_type == 'credit'" role="menuitem" type="object" name="open_action_with_context" context="{'action_name': 'action_credit_statement_tree', 'search_default_journal': True}">Statements</a>
                                        <a t-if="journal_type == 'cash'" role="menuitem" type="object" name="open_action_with_context" context="{'action_name': 'action_view_bank_statement_tree', 'search_default_journal': True}">Cash Registers</a>
                                    </div>
                                    <div>
                                        <a role="menuitem" type="object" name="open_payments_action">Payments</a>
                                    </div>
                                </div>

                                <div class="col-4" groups="account.group_account_basic" name="kanban_manage_new">
                                    <h5 class="o_kanban_card_manage_title">
                                        <span role="separator">New</span>
                                    </h5>
                                    <div name="bank_customer_payment">
                                        <a role="menuitem" type="object" name="create_customer_payment">Customer Payment</a>
                                    </div>
                                    <div>
                                        <a role="menuitem" type="object" name="create_supplier_payment">Vendor Payment</a>
                                    </div>
                                </div>

                            </div>

                            <!-- For purchase and sale -->
                            <div t-if="journal_type == 'purchase' || journal_type == 'sale'" class="row">
                                <div class="col-4">
                                    <h5 class="o_kanban_card_manage_title">
                                        <span>View</span>
                                    </h5>
                                    <div>
                                        <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'action_name': 'action_move_out_invoice_type'}">Invoices</a>
                                        <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'action_name': 'action_move_in_invoice_type'}">Bills</a>
                                    </div>
                                    <div id="sale_purchase_refund">
                                        <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'action_name': 'action_move_out_refund_type'}">Credit Notes</a>
                                        <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'action_name': 'action_move_in_refund_type'}">Refund</a>
                                    </div>
                                    <div>
                                        <a type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}" groups="account.group_account_user">Journal Items</a>
                                    </div>
                                </div>

                                <div class="col-4" groups="account.group_account_invoice" name="kanban_manage_new">
                                    <h5 class="o_kanban_card_manage_title">
                                        <span>New</span>
                                    </h5>
                                    <div>
                                        <a type="object" name="action_create_new">
                                            <span t-if="journal_type == 'sale'">Invoice</span>
                                            <span t-if="journal_type == 'purchase'">Bill</span>
                                        </a>
                                    </div>
                                    <div>
                                        <a type="object" name="action_create_new"  context="{'refund':True}">
                                            <span t-if="journal_type == 'sale'">Credit Note</span>
                                            <span t-if="journal_type == 'purchase'">Refund</span>
                                        </a>
                                    </div>
                                    <div t-if="journal_type == 'sale'">
                                        <widget name="account_file_uploader" title="Upload Invoices" btnClass="file_upload_kanban_action_a"/>
                                    </div>
                                </div>

                                <div class="col-4" name="kanban_manage_reports">
                                    <h5 class="o_kanban_card_manage_title">
                                        <span>Reporting</span>
                                    </h5>
                                    <div>
                                        <a t-if="journal_type == 'sale'" type="action" name="%(action_account_invoice_report_all)d" groups="account.group_account_readonly">Invoices Analysis</a>
                                        <a t-if="journal_type == 'purchase'" type="action" name="%(action_account_invoice_report_all_supp)d" groups="account.group_account_readonly">Bills Analysis</a>
                                    </div>
                                </div>
                            </div>

                            <!-- For general and situation -->
                            <div t-if="journal_type == 'general' || journal_type == 'situation'" class="row" groups="account.group_account_readonly">
                                <div class="col-4">
                                    <h5 class="o_kanban_card_manage_title">
                                        <span>View</span>
                                    </h5>
                                    <div>
                                        <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line'}">Journal Entries</a>
                                    </div>
                                    <div>
                                        <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line', 'search_default_unposted': 1}">Entries to Review</a>
                                    </div>
                                    <div>
                                        <a type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}" groups="base.group_no_one">Journal Items</a>
                                    </div>
                                </div>

                                <div class="col-4" groups="account.group_account_user" name="kanban_manage_new">
                                    <h5 class="o_kanban_card_manage_title">
                                        <span>New</span>
                                    </h5>
                                    <div>
                                        <a type="object" name="action_create_new">Journal Entry</a>
                                    </div>
                                </div>

                                <div class="col-4 o_kanban_manage_operations" groups="account.group_account_user">
                                    <h5 class="o_kanban_card_manage_title">
                                        <span>Operations</span>
                                    </h5>
                                    <div>
                                        <a type="object" name="open_action_with_context" context="{'action_name': 'action_validate_account_move', 'search_default_journal': True}">Post All Entries</a>
                                    </div>
                                </div>
                            </div>

                            <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                                <field class="col-8" name="color" widget="kanban_color_picker"/>
                            </div>

                            <div groups="account.group_account_manager" class="row o_kanban_card_manage_settings">
                                <field name="show_on_dashboard" widget="boolean_favorite" class="col-6"/>
                                <div class="col-6 text-end">
                                    <a class="dropdown-item" t-if="widget.editable" type="edit">Configuration</a>
                                </div>
                            </div>
                        </div>
                    </t>

                    <t t-name="JournalChecks">
                        <div class="row" t-if="dashboard.has_unhashed_entries">
                            <a name="show_unhashed_entries" type="object" class="text-warning">
                                Unhashed entries
                            </a>
                        </div>
                        <div class="row" t-if="dashboard.has_sequence_holes">
                            <a name="show_sequence_holes" type="object" class="text-warning" t-att-title="dashboard.title_has_sequence_holes">
                                Irregular Sequences
                            </a>
                        </div>
                    </t>

                    <t t-name="JournalMiscelaneous">
                        <div id="dashboard_misc_left" class="col mb-3 mb-sm-0">
                            <button id="new_misc_entry_button" type="object" name="action_create_new" class="btn btn-primary float-start me-1" groups="account.group_account_user">
                                New
                            </button>
                        </div>
                        <div id="dashboard_general_right" class="col-auto">
                            <div class="row">
                                <field name="json_activity_data" widget="kanban_vat_activity"/>
                            </div>
                            <t t-if="dashboard.number_to_check > 0">
                                <div class="row">
                                    <div class="col overflow-hidden text-start">
                                        <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line', 'search_default_to_check': True}"><t t-out="dashboard.number_to_check"/> To Check</a>
                                    </div>
                                    <div class="col-auto text-end">
                                        <span dir="ltr"><t t-out="dashboard.to_check_balance"/></span>
                                    </div>
                                </div>
                            </t>
                            <t t-call="JournalChecks"/>
                        </div>
                    </t>

                    <t t-name="JournalBodyBank">
                        <t t-call="JournalBodyBankCash" t-name="bank_configuration_placeholder"/>
                    </t>

                    <t t-name="JournalBodyBankCash">
                        <!-- On the left, display :
                            - A button corresponding to the bank_statements_source, if it wasn't configured, a button for each of them
                            - If there are statements to reconcile, a link to reconcile them -->
                        <div id="dashboard_bank_cash_left" class="col mb-3 mb-sm-0 o_kanban_primary_left">
                            <div name="bank_cash_buttons" class="d-flex gap-1">
                                <t t-set="bank_requires_setup" t-value="!record.bank_account_id.raw_value and ['undefined', 'online_sync'].includes(dashboard.bank_statements_source)"/>
                                <button t-if="journal_type == 'bank' and bank_requires_setup" name="action_configure_bank_journal" type="object" class="btn btn-primary" groups="account.group_account_basic">Bank Setup</button>
                                <button id="all_transactions_btn" type="object" name="open_action" class="btn btn-primary">Transactions</button>
                            </div>
                        </div>
                        <!-- On the right, show other common informations/actions -->
                        <div id="dashboard_bank_cash_right" class="col-auto" name="kanban_primary_right">
                            <div class="row" t-if="dashboard.nb_lines_bank_account_balance > 0">
                                <div id="dashboard_bank_cash_balance" class="col overflow-hidden text-start">
                                    <span>Balance</span>
                                </div>
                                <div class="col-auto text-end">
                                    <span dir="ltr"><t t-out="dashboard.account_balance"/></span>
                                </div>
                            </div>
                            <t t-if="dashboard.has_at_least_one_statement and dashboard.account_balance != dashboard.last_balance">
                                <div class="row" name="latest_statement">
                                    <div class="col overflow-hidden text-start">
                                        <span title="Latest Statement">Last Statement</span>
                                    </div>
                                    <div class="col-auto text-end">
                                        <span dir="ltr"><t t-out="dashboard.last_balance"/></span>
                                    </div>
                                </div>
                            </t>
                            <div class="row" t-if="dashboard.nb_lines_outstanding_pay_account_balance > 0">
                                <div id="dashboard_bank_cash_outstanding_balance" class="col overflow-hidden text-start">
                                    <a
                                        type="action"
                                        name="%(account.action_account_all_payments)d"
                                        context="{'search_default_unmatched': True, 'search_default_journal_id': id, 'search_default_state_posted': True, }"
                                    >
                                        Payments
                                    </a>
                                </div>
                                <div class="col-auto text-end">
                                    <span><t t-out="dashboard.outstanding_pay_account_balance"/></span>
                                </div>
                            </div>
                            <div class="row" t-if="dashboard.nb_misc_operations > 0" groups="account.group_account_readonly">
                                <div id="dashboard_bank_cash_misc_total" class="col text-start">
                                    <a type="object" name="open_bank_difference_action" t-att-class="dashboard.misc_class">Misc. Operations</a>
                                </div>
                                <div class="col-auto text-end">
                                    <span dir="ltr"><t t-out="dashboard.misc_operations_balance"/></span>
                                </div>
                            </div>
                            <t t-call="JournalChecks"/>
                        </div>
                    </t>
                    <t t-name="JournalBodySalePurchase" id="account.JournalBodySalePurchase">
                        <div class="col mb-3 mb-sm-0" name="kanban_primary_left">
                            <t t-if="journal_type == 'sale'">
                                <button type="object" name="action_create_new" class="btn btn-primary" groups="account.group_account_invoice">
                                    New
                                </button>
                                <widget name="account_onboarding" t-if="journal_is_empty and dashboard['onboarding']"/>
                            </t>
                            <t t-if="journal_type == 'purchase'">
                                <div t-if="!journal_is_empty" class="d-flex">
                                    <widget name="account_file_uploader" btnClass="btn btn-primary oe_kanban_action"/>
                                    <div class="ms-1">
                                        <button type="object" name="action_create_new" class="btn-secondary " groups="account.group_account_invoice">New</button>
                                    </div>
                                </div>
                                <div t-else="">
                                    <widget name="account_file_uploader" btnClass="btn btn-primary oe_kanban_action"/>
                                    <widget name="bill_upload_guide"/>
                                </div>
                            </t>
                        </div>
                        <div t-if="!journal_is_empty" class="col-auto" name="kanban_primary_right">
                            <div class="row" t-if="dashboard.number_draft">
                                <div class="col overflow-hidden text-start">
                                    <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'search_default_draft': '1'}">
                                        <span title="Invoices to Validate"><t t-out="dashboard.number_draft"/> To Validate</span>
                                    </a>
                                    <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'search_default_draft': '1'}">
                                        <span title="Bills to Validate" id="account_dashboard_purchase_draft"><t t-out="dashboard.number_draft"/> To Validate</span>
                                    </a>
                                </div>
                                <div class="col-auto text-end">
                                    <span dir="ltr"><t t-out="dashboard.sum_draft"/></span>
                                </div>
                            </div>
                            <div class="row" t-if="dashboard.number_waiting">
                                <div class="col overflow-hidden text-start">
                                    <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'search_default_open': '1', 'search_default_posted': '1', 'journal_type': type}">
                                        <span><t t-out="dashboard.number_waiting"/> Unpaid</span>
                                    </a>
                                    <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'search_default_open': '1', 'search_default_posted': '1', 'journal_type': type}">
                                        <span title="Bills to Pay" id="account_dashboard_bills_to_pay"><t t-out="dashboard.number_waiting"/> To Pay</span>
                                    </a>
                                </div>
                                <div class="col-auto text-end">
                                    <span dir="ltr"><t t-out="dashboard.sum_waiting"/></span>
                                </div>
                            </div>
                            <div class="row" t-if="dashboard.number_late">
                                <div class="col overflow-hidden text-start">
                                    <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'search_default_late': '1', 'search_default_posted': '1', 'journal_type': type}">
                                        <span title="Invoices late" id="account_dashboard_invoices_late">
                                            <t t-out="dashboard.number_late"/> Late
                                        </span>
                                    </a>
                                    <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'search_default_late': '1', 'search_default_posted': '1', 'journal_type': type}">
                                        <span title="Bills Late" id="account_dashboard_bills_late">
                                            <t t-out="dashboard.number_late"/> Late
                                        </span>
                                    </a>
                                </div>
                                <div class="col-auto text-end">
                                    <span dir="ltr"><t t-out="dashboard.sum_late"/></span>
                                </div>
                            </div>
                            <t t-if="dashboard.number_to_check > 0">
                                <div class="row">
                                    <div class="col overflow-hidden text-start">
                                        <a type="object" name="open_action" context="{'search_default_to_check': True}"><t t-out="dashboard.number_to_check"/> To Check</a>
                                    </div>
                                    <div class="col-auto text-end">
                                        <span dir="ltr"><t t-out="dashboard.to_check_balance"/></span>
                                    </div>
                                </div>
                            </t>
                            <t t-call="JournalChecks"/>
                        </div>
                    </t>
                    <t t-name="JournalBodyGraph">
                        <field name="kanban_dashboard_graph" t-att-graph_type="['cash', 'bank', 'credit'].includes(journal_type) ? 'line' : 'bar'" widget="dashboard_graph"/>
                    </t>
            </templates>
            </kanban>
        </field>
    </record>

    <record id="open_account_journal_dashboard_kanban" model="ir.actions.act_window">
        <field name="name">Dashboard</field>
        <field name="path">accounting</field>
        <field name="res_model">account.journal</field>
        <field name="view_mode">kanban,form</field>
        <field name="view_id" ref="account_journal_dashboard_kanban_view"/>
        <field name="usage">menu</field>
        <field name="context">{'search_default_dashboard':1}</field>
        <field name="domain">[]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_neutral_face">
                This is the accounting dashboard
            </p><p>
                If you have not installed a chart of account, please install one first.<br/>
               <a class="btn-link" type="action" name="%(open_account_charts_modules)d" tabindex="-1">Browse available countries.</a>
            </p>
        </field>
    </record>

</odoo>
