<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">
    <record id="certificate_rule_company" model="ir.rule">
        <field name="name">Certificate multi-company</field>
        <field name="model_id" ref="model_certificate_certificate"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'parent_of', company_ids)]</field>
    </record>

    <record id="key_rule_company" model="ir.rule">
        <field name="name">Key multi-company</field>
        <field name="model_id" ref="model_certificate_key"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'parent_of', company_ids)]</field>
    </record>
</data>
</odoo>
